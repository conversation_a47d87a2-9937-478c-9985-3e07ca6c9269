# Wings Customer Import Plugin

A comprehensive WordPress plugin for importing customers from Wings Portal API with real-time progress tracking.

## 📁 Plugin Structure

```
wings-customer-import/
├── wings-customer-import.php          # Main plugin file
├── readme.txt                         # WordPress plugin readme
├── README.md                          # This file
├── includes/                          # Core functionality
│   ├── class-wings-api.php           # Wings API communication
│   └── class-wings-importer.php      # Import processing logic
├── admin/                             # Admin interface
│   └── class-wings-admin.php         # Admin pages and forms
├── assets/                            # Frontend assets
│   ├── css/
│   │   └── admin.css                 # Admin styles
│   └── js/
│       └── admin.js                  # Admin JavaScript
└── languages/                        # Translation files (future)
    └── wings-customer-import.pot     # Translation template
```

## 🚀 Installation

### Method 1: Upload to Plugins Directory

1. **Upload the entire folder** to `/wp-content/plugins/`:
   ```
/wp-content/plugins/wings-customer-import/
```

2. **Activate the plugin** in WordPress Admin:
   - Go to `Plugins → Installed Plugins`
   - Find "Wings Customer Import"
   - Click "Activate"

3. **Configure settings**:
   - Go to `Wings Import → Settings`
   - Enter your Wings API credentials
   - Test the connection

### Method 2: ZIP Installation

1. **Create a ZIP file** of the entire `wings-customer-import` folder
2. **Upload via WordPress Admin**:
   - Go to `Plugins → Add New → Upload Plugin`
   - Choose the ZIP file
   - Click "Install Now" then "Activate"

## ⚙️ Configuration

### Required Settings

| Setting | Description | Example |
|---------|-------------|---------|
| **API URL** | Wings Portal API base URL | `https://portal.wings.rs/api/v1/` |
| **API Alias** | Your Wings Portal alias | `grosstest` |
| **API Username** | Wings API username | `aql` |
| **API Password** | Wings API password | `grossaql` |
| **Batch Size** | Customers per batch (recommended: 25) | `25` |
| **Default Role** | WordPress role for imported users | `Professionals` |

### Configuration Steps

1. **Navigate to Settings**:
   ```
WordPress Admin → Wings Import → Settings
```

2. **Fill in API credentials**:
   - Get these from your Wings Portal administrator
   - Test connection before importing

3. **Adjust batch size**:
   - Smaller batches = more stable (recommended: 25)
   - Larger batches = faster but may timeout

## 🎯 Usage

### Starting an Import

1. **Go to Import Dashboard**:
   ```
WordPress Admin → Wings Import
```

2. **Verify configuration** in the form fields

3. **Click "Start Import"** button

4. **Monitor progress** in real-time:
   - Progress bar shows completion percentage
   - Statistics show created/updated/errors
   - Live log shows detailed progress
   - Error details appear if issues occur

### Import Process

The plugin follows this process:

1. **Authentication** - Connects to Wings API
2. **Customer Count** - Gets total number of customers
3. **Batch Processing** - Processes customers in small batches
4. **Data Mapping** - Maps Wings data to WordPress fields
5. **User Creation/Update** - Creates new users or updates existing
6. **Completion** - Shows final statistics and logs

### Check/Create Customer Feature

The plugin now includes a powerful feature to check if customers exist in Wings and optionally create them:

1. **Navigate to Manage Customers**:
   ```
WordPress Admin → Wings Import → Manage Customers
```

2. **Use the Check/Create form** at the top of the page:
   - Enter company name (naziv) - required
   - Fill optional fields (email, contact, phone, address, etc.)
   - Check "Create customer in Wings if not found" to auto-create
   - Click "Check Customer"

3. **Results**:
   - ✅ **Found**: Shows existing customer details
   - ➕ **Created**: Creates new customer if checkbox was checked
   - ⚠️ **Not Found**: Shows customer doesn't exist

**Use Cases**:
- Verify customer existence before manual entry
- Quick customer creation from WordPress admin
- Prevent duplicate customer creation
- Customer lookup by company name

For detailed information, see [CHECK-CREATE-CUSTOMER.md](CHECK-CREATE-CUSTOMER.md).

## 📊 Data Mapping

### Wings → WordPress User Fields

| Wings Field | WordPress Field | Description |
|-------------|-----------------|-------------|
| `naziv` | `display_name`, `nickname` | Company/customer name |
| `email` | `user_email` | Email (generated if missing) |
| `kontakt` | `first_name`, `last_name` | Contact person name |
| `adresa` | `billing_address_1` | Street address |
| `mesto` | `billing_city`, `billing_postcode` | City and postal code |
| `telefon`/`mobilni` | `billing_phone` | Phone number |

### Wings → User Meta Fields

| Wings Field | Meta Key | Description |
|-------------|----------|-------------|
| `id` | `wings_customer_id` | Wings customer ID |
| `sifra` | `wings_customer_code` | Customer code |
| `pib` | `wings_pib` | Tax ID |
| `status` | `wings_status` | Customer status |
| `komercijalista` | `wings_sales_rep` | Sales representative |
| `klasa` | `wings_customer_class` | Customer class |
| `rabat` | `wings_discount` | Discount percentage |
| `limit` | `wings_credit_limit` | Credit limit |

## 🔧 Technical Details

### System Requirements

- **WordPress**: 5.0+
- **PHP**: 7.4+
- **Memory**: 256MB+ recommended
- **Execution Time**: 300+ seconds for large imports
- **Network**: External API access required

### Performance Specifications

- **Batch Size**: 25 customers per API call (configurable)
- **Processing Speed**: ~100-150 customers per minute
- **Memory Usage**: Optimized with log rotation
- **Session Timeout**: 2 hours (sufficient for large imports)
- **Estimated Time**: 15-20 minutes for 2,000 customers

### Security Features

- **WordPress Authentication** - Requires admin privileges
- **Nonce Verification** - CSRF protection
- **Input Sanitization** - All data sanitized
- **Capability Checks** - `manage_options` required
- **Session Management** - Secure session handling

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Failed**:
   - Verify API credentials
   - Check Wings Portal access
   - Test connection in settings

2. **Import Timeouts**:
   - Reduce batch size to 10-15
   - Check server PHP limits
   - Monitor server resources

3. **Memory Issues**:
   - Increase PHP memory limit
   - Reduce batch size
   - Clear old import sessions

4. **API Connection Errors**:
   - Check server firewall settings
   - Verify external URL access
   - Test Wings Portal availability

### Debug Information

Check these locations for debugging:

1. **Plugin Logs**:
   ```
Wings Import → Import Logs
```

2. **WordPress Debug Log**:
   ```
/wp-content/debug.log
```

3. **Server Error Logs**:
   ```
Check your hosting control panel
```

## 📝 Logging

The plugin maintains detailed logs:

- **Session Logs** - Stored in WordPress transients
- **Database Logs** - Permanent logs in custom table
- **Live Logs** - Real-time display during import
- **Error Details** - Specific error information

### Log Levels

- **Info** - General information
- **Error** - Import errors
- **Warning** - Non-critical issues
- **Success** - Successful operations

## 🔄 Updates

### Version History

- **1.0.0** - Initial release with full functionality

### Future Enhancements

- Multi-language support
- Export functionality
- Advanced filtering options
- Scheduled imports
- Email notifications

## 📞 Support

For technical support:

1. **Check Documentation** - Review this README
2. **Check Logs** - Look at import logs for errors
3. **Test Connection** - Verify API connectivity
4. **Contact Support** - Reach out with specific error messages

## 🤝 Contributing

To contribute to this plugin:

1. Follow WordPress coding standards
2. Test with various data sets
3. Document any changes
4. Submit pull requests with clear descriptions

## 📄 License

This plugin is licensed under GPL v2 or later.

---

**Ready to import your Wings customers?** 🚀

1. Install the plugin
2. Configure your API settings
3. Test the connection
4. Start importing!

The plugin will handle the rest with real-time progress tracking and comprehensive error handling.
 "111408085",
        "rabat": 0.0,
        "komercijalista": "4050",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11326",
      "attributes": {
        "sifra": "11326",
        "naziv": "AČANSKI S.T.R.*",
        "adresa": "ŠAFARIKOVA 73",
        "mesto": "21400 BAČKA PALANKA",
        "kontakt": "",
        "telefon": "021/744-113P;741-452K",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "3047",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12419",
      "attributes": {
        "sifra": "12419",
        "naziv": "ADŽIĆ OPTIKA */* AKS",
        "adresa": "Užička 23",
        "mesto": "14000 Valjevo",
        "kontakt": "",
        "telefon": "014/244-832, 015/558-046 Koceljeva",
        "fax": "Žaklina 065/465-3386",
        "mobilni": "014/221-859 K",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "108690908",
        "rabat": 0.0,
        "komercijalista": "3047",
        "mb": "",
        "limit": 2000.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 30,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11446",
      "attributes": {
        "sifra": "11446",
        "naziv": "AIR SPEED MP",
        "adresa": "",
        "mesto": "11000 BEOGRAD",
        "kontakt": "",
        "telefon": "063/231-248",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11394",
      "attributes": {
        "sifra": "11433",
        "naziv": "AKIOPTIK /+",
        "adresa": "Stevana Nemanje 26",
        "mesto": "36300 Novi Pazar",
        "kontakt": "Karaahmetović Alan, Ševala optom.",
        "telefon": "020/312-322r",
        "fax": "",
        "mobilni": "063/312-322",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "4050",
        "mb": "",
        "limit": 100.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12928",
      "attributes": {
        "sifra": "129015",
        "naziv": "akj",
        "adresa": "",
        "mesto": "",
        "kontakt": "",
        "telefon": "",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12589",
      "attributes": {
        "sifra": "14107",
        "naziv": "AL MAGNUS D.O.O. MP post expres",
        "adresa": "Dimitrija Tucovića 16",
        "mesto": "11420 Smederevska Palanka",
        "kontakt": "Aleksandar 063/7200-381",
        "telefon": "026/312-136 K",
        "fax": "Dr Mihajlović 026/314-450 ordin.",
        "mobilni": "063/311-088",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "4050",
        "mb": "",
        "limit": 3200.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 30,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11227",
      "attributes": {
        "sifra": "13515",
        "naziv": "ALBA GRECA +/+",
        "adresa": "Vojvode Stepe 299-g",
        "mesto": "11000 Beograd",
        "kontakt": "",
        "telefon": "3097-877",
        "fax": "063/223-455",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "4050",
        "mb": "",
        "limit": 2000.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 30,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11719",
      "attributes": {
        "sifra": "11719",
        "naziv": "ALBA-LENS",
        "adresa": "PREDSTAVNIŠTVO BEOGRAD",
        "mesto": "YU BUSINESS CENTER",
        "kontakt": "",
        "telefon": "311-61-92",
        "fax": "147-028",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "14119",
      "attributes": {
        "sifra": "14119",
        "naziv": "ALEKSANDAR TODOROVIĆ",
        "adresa": "Hilandarska 30",
        "mesto": "",
        "kontakt": "DOSTAVA D2",
        "telefon": "064/115-4829",
        "fax": "dr Tasev",
        "mobilni": "PRIVREMENA KARTICA",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "3047",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "17629",
      "attributes": {
        "sifra": "17629",
        "naziv": "ALEKSANDAR URUMOVIĆ PR VERA 011 MP",
        "adresa": "Milana Jovanovića 14A, Čukarica",
        "mesto": "",
        "kontakt": "",
        "telefon": "",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11369",
      "attributes": {
        "sifra": "13768",
        "naziv": "ALFA - OPT. SERVIS *",
        "adresa": "Jovana Kursule bb",
        "mesto": "37260 Varvarin",
        "kontakt": "",
        "telefon": "037/787-641",
        "fax": "",
        "mobilni": "062/733-676",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "32",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12342",
      "attributes": {
        "sifra": "12342",
        "naziv": "ALFA OPTIK *",
        "adresa": "Miloša Obrenovića 21",
        "mesto": "11500 Obrenovac",
        "kontakt": "Jovanović",
        "telefon": "",
        "fax": "",
        "mobilni": "064/2282565",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "42",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12700",
      "attributes": {
        "sifra": "120168",
        "naziv": "ALFABO D.O.O.",
        "adresa": "27. marta 23",
        "mesto": "11000 Beograd",
        "kontakt": "",
        "telefon": "303-5944",
        "fax": "",
        "mobilni": "063/250-524",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "104891530",
        "rabat": 0.0,
        "komercijalista": "42",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12804",
      "attributes": {
        "sifra": "12804",
        "naziv": "ALMA OPTIK *",
        "adresa": "Vikend zona BB, Milutinovac",
        "mesto": "19328 Velesnica",
        "kontakt": "DUŠAN NISIC",
        "telefon": "019/803-529",
        "fax": "",
        "mobilni": "063/8490-763",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "102120636",
        "rabat": 0.0,
        "komercijalista": "42",
        "mb": "",
        "limit": 900.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11069",
      "attributes": {
        "sifra": "11069",
        "naziv": "ALOJZIJE LAJOS",
        "adresa": "FOČANSKA 35",
        "mesto": "24000 SUBOTICA",
        "kontakt": "",
        "telefon": "024/38-847",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "2045",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12901",
      "attributes": {
        "sifra": "129011",
        "naziv": "AMBRA OPTICS ITALIA S.R.L.",
        "adresa": "STR. VETERANILOR 11/A SECTOR 6",
        "mesto": "BUCAREST ROMANIA",
        "kontakt": "TIZIANO PANCRAZI",
        "telefon": "0040-724060907",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11782",
      "attributes": {
        "sifra": "13214",
        "naziv": "AMI EUROLINE D.O.O.",
        "adresa": "Kneza Miloša 80/7, II sprat",
        "mesto": "11000 Beograd",
        "kontakt": "063/235-600",
        "telefon": "316-2247R;3610-625 f;687-297 f",
        "fax": "lok 171 Sava Centar",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "16280",
      "attributes": {
        "sifra": "16280",
        "naziv": "AMIĆI 2015 DOO +/+ AKS F",
        "adresa": "Kneza Miloša 10",
        "mesto": "15300 Loznica",
        "kontakt": "Ivana",
        "telefon": "064/574-44-18",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "109486",
        "rabat": 0.0,
        "komercijalista": "3047",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "13014",
      "attributes": {
        "sifra": "129562",
        "naziv": "AMN LENS DOO",
        "adresa": "Beogradska 45",
        "mesto": "11000 Beograd",
        "kontakt": "Laketić Milorad",
        "telefon": "011-334-97-39; 063-105-98-35",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "107374251",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 0.0,
        "racun": "160-369414-59",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12876",
      "attributes": {
        "sifra": "128761",
        "naziv": "ANA DUDVARSKI",
        "adresa": "13.Oktobra 13",
        "mesto": "Umka-Beograd",
        "kontakt": "",
        "telefon": "",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "42",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12945",
      "attributes": {
        "sifra": "128883",
        "naziv": "ANLEK d.o.o. +",
        "adresa": "Ljubinke Bobić 28",
        "mesto": "Novi Beograd",
        "kontakt": "Danijela Sretenović",
        "telefon": "011-2166-307",
        "fax": "011-2175-157",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "100394478",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 2200.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 15,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "17552",
      "attributes": {
        "sifra": "17552",
        "naziv": "ANĐELIJA DURMAN PR MP",
        "adresa": "Miroslava Antića 12",
        "mesto": "Novi Sad",
        "kontakt": "Anđelija",
        "telefon": "",
        "fax": "",
        "mobilni": "063/520-203",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "107305526",
        "rabat": 0.0,
        "komercijalista": "3047",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "16245",
      "attributes": {
        "sifra": "16245",
        "naziv": "APOTEKA 'MEDICA' +",
        "adresa": "Momira Gajića br. 2",
        "mesto": "11320 Velika Plana",
        "kontakt": "Dragana Bajović, Ogranak Centar",
        "telefon": "026/4100-244; 025/540-880, 540-881",
        "fax": "JBKJS 80129 /026-516-169 racun.",
        "mobilni": "065-52-11-690 Gordana dir.",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "104803868",
        "rabat": 0.0,
        "komercijalista": "6",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 60,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12986",
      "attributes": {
        "sifra": "13201",
        "naziv": "APOTEKA ASTRA-ne postoji više",
        "adresa": "Bulevar Kralja Aleksandra 138",
        "mesto": "11000 Beograd",
        "kontakt": "Tatjana Miladinović",
        "telefon": "011-3443-580",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12946",
      "attributes": {
        "sifra": "12601",
        "naziv": "APOTEKA ĆETKOVIĆ",
        "adresa": "Ljubinska 5a - CERAK",
        "mesto": "11000 BEOGRAD",
        "kontakt": "Zoran",
        "telefon": "011/500-803",
        "fax": "063/223-826",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 100.0,
        "racun": "",
        "rokplacanja": 7,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12987",
      "attributes": {
        "sifra": "13683",
        "naziv": "APOTEKA DEA",
        "adresa": "CETINJSKA 9",
        "mesto": "11000 BEOGRAD",
        "kontakt": "Zorica Milovanović",
        "telefon": "32370-32",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12988",
      "attributes": {
        "sifra": "13226",
        "naziv": "APOTEKA DEA FARM+ zatvorena",
        "adresa": "CARICE MILICE 16",
        "mesto": "11000 BEOGRAD",
        "kontakt": "Jasmina Rusić,Marica,Mirjana",
        "telefon": "2623-581",
        "fax": "180-896",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12989",
      "attributes": {
        "sifra": "13277",
        "naziv": "APOTEKA DREN FARMA",
        "adresa": "Stanoja Glavaša 13 (kod Palilulske",
        "mesto": "pijace)",
        "kontakt": "Biljana Iričanin",
        "telefon": "011-334-77-44",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "16293",
      "attributes": {
        "sifra": "16293",
        "naziv": "APOTEKA FARMACIA +",
        "adresa": "Šumadijske divizije br. 6",
        "mesto": "Beograd, 11000",
        "kontakt": "Aleksandra Jevtić",
        "telefon": "011/770-45-54",
        "fax": "",
        "mobilni": "060/0822-889",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "109759159",
        "rabat": 0.0,
        "komercijalista": "6",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "17326",
      "attributes": {
        "sifra": "17326",
        "naziv": "APOTEKA KRALJEVO +",
        "adresa": "TRG SRPSKIH RATNIKA 14",
        "mesto": "36000 KRALJEVO",
        "kontakt": "0638145350 Marijana Majstorović",
        "telefon": "036-325-900, 036-325-910",
        "fax": "",
        "mobilni": "apoteka 036-315-630 Marijana",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "6",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 60,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "17356",
      "attributes": {
        "sifra": "17356",
        "naziv": "APOTEKA REMEDIJA +",
        "adresa": "RIFATA BURDŽOVIĆA 13",
        "mesto": "36300 NOVI PAZAR",
        "kontakt": "Ajša Dervišević",
        "telefon": "************",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "101797351",
        "rabat": 0.0,
        "komercijalista": "6",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 60,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "15191",
      "attributes": {
        "sifra": "15191",
        "naziv": "APOTEKA UNA +",
        "adresa": "Crvenog krsta 7",
        "mesto": "Kragujevac 34000",
        "kontakt": "Gorica Ćalasan",
        "telefon": "034-6337-079",
        "fax": "034-6337-079",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "100561869",
        "rabat": 0.0,
        "komercijalista": "10",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "16256",
      "attributes": {
        "sifra": "16256",
        "naziv": "APOTEKA VESNA +",
        "adresa": "Salakovac bb",
        "mesto": "12311 Malo Crniće",
        "kontakt": "Vesna Stojićević",
        "telefon": "012/280-848",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "101336574",
        "rabat": 0.0,
        "komercijalista": "6",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 7,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12947",
      "attributes": {
        "sifra": "13647",
        "naziv": "APOTEKA VUČETIĆ +",
        "adresa": "Kneza Miloša 17",
        "mesto": "11000 BEOGRAD",
        "kontakt": "Dejan",
        "telefon": "324-0090",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12923",
      "attributes": {
        "sifra": "12923",
        "naziv": "ARCO SRL",
        "adresa": "Via di Partonaccio 23/B",
        "mesto": "00159 Roma Italia",
        "kontakt": "Luciano Asiani",
        "telefon": "+39 06 4393449",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "0",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "17544",
      "attributes": {
        "sifra": "17544",
        "naziv": "ARS NOVA DOO",
        "adresa": "Gandijeva 11/14",
        "mesto": "Beograd",
        "kontakt": "Irena",
        "telefon": "060/604-0430",
        "fax": "011/312-21-22",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "101017808",
        "rabat": 0.0,
        "komercijalista": "2045",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "17520",
      "attributes": {
        "sifra": "17520",
        "naziv": "AS GLASSES NOVI SAD, +",
        "adresa": "Braće Dronjak 7,",
        "mesto": "21000 Novi Sad",
        "kontakt": "SOFIJA ČEREVICKI",
        "telefon": "0641477062",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "112568504",
        "rabat": 0.0,
        "komercijalista": "3047",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12044",
      "attributes": {
        "sifra": "13138",
        "naziv": "AS PRINT+",
        "adresa": "Mozerova 34 (bivša Sime Šolaje)",
        "mesto": "11080 Zemun",
        "kontakt": "Milka Đukić",
        "telefon": "316-35-66",
        "fax": "316-22-06",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 15,
        "klasa": 0
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "12482",
      "attributes": {
        "sifra": "14074",
        "naziv": "ASKOD MP POST EXPRESS",
        "adresa": "Pionirski trg 20",
        "mesto": "12000 Požarevac",
        "kontakt": "Dragoljub Konstadinović",
        "telefon": "012/532-656 r; 222-656 k",
        "fax": "",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "4050",
        "mb": "",
        "limit": 0.0,
        "racun": "",
        "rokplacanja": 0,
        "tolerancija": 0,
        "klasa": 2
      }
    },
    {
      "type": "local-kupac-svi",
      "id": "11895",
      "attributes": {
        "sifra": "12893",
        "naziv": "ASTRON OPTIC +",
        "adresa": "Trgovačka 18",
        "mesto": "11000 Beograd",
        "kontakt": "",
        "telefon": "239-39-67p;686-855k",
        "fax": "064/ 244 81 41",
        "mobilni": "",
        "email": "",
        "radnovreme": "",
        "status": "K",
        "pib": "",
        "rabat": 0.0,
        "komercijalista": "43",
        "mb": "",
        "limit": 1600.0,
        "racun": "",
        "rokplacanja": 30,
        "tolerancija": 30,
        "klasa": 2
      }
    }
  ]
}