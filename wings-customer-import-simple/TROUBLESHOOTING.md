# Wings Customer Import - Simple: Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Issue 1: Cannot See Imported Customers

**Symptoms:**
- Manage Customers page shows "Loading customers..." or empty table
- Import appears successful but no customers visible

**Possible Causes & Solutions:**

#### A. Missing Wings Customer ID Meta
```php
// Check if users have wings_customer_id meta
$users = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'wings_customer_id',
            'compare' => 'EXISTS'
        )
    )
));
echo "Users with wings_customer_id: " . count($users);
```

**Solution:** Re-run the import to ensure meta fields are set correctly.

#### B. Wrong User Role
The plugin looks for users with specific roles. Check if imported users have the correct role:

```php
// Check user roles
$users = get_users(array('number' => 10));
foreach ($users as $user) {
    echo "User: {$user->display_name} - Roles: " . implode(', ', $user->roles) . "\n";
}
```

**Solution:** Update user roles to 'Professionals', 'customer', or 'subscriber'.

#### C. JavaScript/AJAX Issues
- Open browser Developer Tools (F12)
- Check Console for JavaScript errors
- Check Network tab for failed AJAX requests

**Solution:** Ensure jQuery is loaded and no JavaScript conflicts exist.

### Issue 2: Customer Creation Fails

**Symptoms:**
- "Check Customer" works but creation fails
- Error messages about API calls

**Debugging Steps:**

#### A. Test API Connection
Use the test script: `/wp-content/plugins/wings-customer-import-simple/test-api.php`

#### B. Check API Credentials
```php
$settings = get_option('wings_import_settings', array());
print_r($settings);
```

#### C. Enable Debug Logging
Add to wp-config.php:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check `/wp-content/debug.log` for Wings API errors.

#### D. Manual API Test
```php
// Test authentication
$login_url = 'https://portal.wings.rs/api/v1/grosstest/system.user.log';
$response = wp_remote_post($login_url, array(
    'body' => array(
        'aUn' => 'your_username',
        'aUp' => 'your_password'
    )
));

// Check response
echo "Response code: " . wp_remote_retrieve_response_code($response);
echo "Response body: " . wp_remote_retrieve_body($response);
```

### Issue 3: Search Not Working

**Symptoms:**
- Customer search returns "not found" for existing customers
- Search parameters not working correctly

**Solutions:**

#### A. Check Search Parameter Format
The Wings API might expect different search parameter formats:

```php
// Try different formats
$search_params = array(
    'dStart' => 0,
    'dLength' => 100,
    'search[value]' => $naziv,  // Format 1
    'search[regex]' => 'false',
    'output' => 'jsonapi'
);

// Or
$search_params = array(
    'dStart' => 0,
    'dLength' => 100,
    'search' => $naziv,  // Format 2
    'output' => 'jsonapi'
);
```

#### B. Case Sensitivity Issues
Ensure case-insensitive matching:
```php
if (trim(strtolower($customer['attributes']['naziv'])) === trim(strtolower($naziv))) {
    return $customer;
}
```

### Issue 4: Authentication Problems

**Symptoms:**
- "Authentication failed" errors
- Session cookie issues

**Solutions:**

#### A. Check API Credentials
- Verify username/password are correct
- Test login manually in Wings Portal
- Check if account has API access

#### B. Session Cookie Issues
```php
// Debug session cookies
$cookies = wp_remote_retrieve_cookies($auth_response);
foreach ($cookies as $cookie) {
    echo "Cookie: {$cookie->name} = {$cookie->value}\n";
}
```

#### C. Timeout Issues
Increase timeout values:
```php
$response = wp_remote_post($url, array(
    'timeout' => 60,  // Increase from 30
    'body' => $data
));
```

## 🔧 Debug Tools

### 1. Debug Script
Use `/wp-content/plugins/wings-customer-import-simple/debug-customers.php` to:
- Check user meta fields
- Test API connection
- Verify customer data

### 2. Test API Script
Use `/wp-content/plugins/wings-customer-import-simple/test-api.php` to:
- Test authentication
- Test customer creation
- Manual API testing

### 3. Browser Developer Tools
- **Console Tab**: Check for JavaScript errors
- **Network Tab**: Monitor AJAX requests
- **Application Tab**: Check for session storage issues

### 4. WordPress Debug Log
Enable debugging and check `/wp-content/debug.log` for:
- PHP errors
- Wings API request/response logs
- Authentication issues

## 🔍 Common Error Messages

### "Authentication failed"
- Check API credentials in Settings
- Verify Wings Portal access
- Test manual login to Wings Portal

### "No session cookie received"
- API authentication succeeded but no session
- Check Wings Portal API configuration
- Verify cookie handling in wp_remote_post

### "Customer creation failed"
- Check required fields (naziv is mandatory)
- Verify API endpoint URL
- Check request format and parameters

### "API request failed"
- Network connectivity issues
- Timeout problems
- Invalid API URL or alias

## 🚀 Quick Fixes

### Reset Plugin Settings
```php
delete_option('wings_import_settings');
// Then reconfigure in Settings page
```

### Clear User Meta
```php
// Remove all Wings meta from users (use carefully!)
$users = get_users();
foreach ($users as $user) {
    delete_user_meta($user->ID, 'wings_customer_id');
    delete_user_meta($user->ID, 'wings_pib');
    delete_user_meta($user->ID, 'wings_status');
}
```

### Force Customer Display
Temporarily modify the customer retrieval to show all users:
```php
// In ajax_get_customers(), comment out meta_query and use:
$args = array(
    'number' => 20,
    'offset' => ($page - 1) * 20
);
```

## 📞 Getting Help

1. **Check Debug Logs**: Always check `/wp-content/debug.log` first
2. **Use Test Scripts**: Run the provided debug and test scripts
3. **Browser Console**: Check for JavaScript errors
4. **API Documentation**: Verify Wings API endpoint requirements
5. **WordPress Forums**: Search for similar issues

## 🔄 Step-by-Step Debugging Process

1. **Verify Settings**: Check API credentials in Settings page
2. **Test Connection**: Use test-api.php to verify authentication
3. **Check Users**: Use debug-customers.php to see user data
4. **Browser Console**: Check for JavaScript errors
5. **Debug Logs**: Enable WordPress debugging and check logs
6. **Manual Testing**: Try manual API calls to isolate issues
7. **Reset if Needed**: Clear settings and reconfigure

Remember: Most issues are related to API credentials, user meta fields, or request formatting. Start with the basics and work your way up!
