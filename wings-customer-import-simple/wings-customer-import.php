<?php
/**
 * Plugin Name: Wings Customer Import - Simple
 * Description: Simple Wings Portal API customer import with management interface
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
define('WINGS_IMPORT_VERSION', '1.0.0');
define('WINGS_IMPORT_URL', plugin_dir_url(__FILE__));
define('WINGS_IMPORT_PATH', plugin_dir_path(__FILE__));

/**
 * Main Plugin Class
 */
class Wings_Customer_Import {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_wings_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_wings_import_customers', array($this, 'ajax_import_customers'));
        add_action('wp_ajax_wings_get_customers', array($this, 'ajax_get_customers'));
        add_action('wp_ajax_wings_update_customer', array($this, 'ajax_update_customer'));
        add_action('wp_ajax_wings_delete_customer', array($this, 'ajax_delete_customer'));
        add_action('wp_ajax_wings_check_create_customer', array($this, 'ajax_check_create_customer'));
        
        register_activation_hook(__FILE__, array($this, 'activate'));
    }
    
    public function activate() {
        add_option('wings_import_settings', array(
            'api_url' => 'https://portal.wings.rs/api/v1/',
            'api_alias' => 'grosstest',
            'api_username' => 'aql',
            'api_password' => 'grossaql',
            'batch_size' => 25,
            'default_role' => 'Professionals'
        ));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Wings Import',
            'Wings Import',
            'manage_options',
            'wings-import',
            array($this, 'dashboard_page'),
            'dashicons-download',
            30
        );
        
        add_submenu_page(
            'wings-import',
            'Import Dashboard',
            'Import Dashboard',
            'manage_options',
            'wings-import',
            array($this, 'dashboard_page')
        );
        
        add_submenu_page(
            'wings-import',
            'Manage Customers',
            'Manage Customers',
            'manage_options',
            'wings-manage-customers',
            array($this, 'manage_customers_page')
        );
        
        add_submenu_page(
            'wings-import',
            'Settings',
            'Settings',
            'manage_options',
            'wings-settings',
            array($this, 'settings_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if (strpos($hook, 'wings') === false) {
            return;
        }
        
        wp_enqueue_script('wings-admin', WINGS_IMPORT_URL . 'assets/admin.js', array('jquery'), WINGS_IMPORT_VERSION, true);
        wp_enqueue_style('wings-admin', WINGS_IMPORT_URL . 'assets/admin.css', array(), WINGS_IMPORT_VERSION);
        
        wp_localize_script('wings-admin', 'wingsAjax', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wings_nonce')
        ));
    }
    
    public function dashboard_page() {
        $settings = get_option('wings_import_settings', array());
        ?>
        <div class="wrap">
            <h1>Wings Customer Import</h1>
            
            <div class="wings-dashboard">
                <div class="wings-config">
                    <h2>Configuration</h2>
                    <table class="form-table">
                        <tr>
                            <th>API URL:</th>
                            <td><input type="text" id="api_url" value="<?php echo esc_attr($settings['api_url']); ?>" class="regular-text" /></td>
                        </tr>
                        <tr>
                            <th>Alias:</th>
                            <td><input type="text" id="api_alias" value="<?php echo esc_attr($settings['api_alias']); ?>" class="regular-text" /></td>
                        </tr>
                        <tr>
                            <th>Username:</th>
                            <td><input type="text" id="api_username" value="<?php echo esc_attr($settings['api_username']); ?>" class="regular-text" /></td>
                        </tr>
                        <tr>
                            <th>Password:</th>
                            <td><input type="password" id="api_password" value="<?php echo esc_attr($settings['api_password']); ?>" class="regular-text" /></td>
                        </tr>
                    </table>
                    
                    <p>
                        <button id="test-connection" class="button button-secondary">Test Connection</button>
                        <button id="start-import" class="button button-primary">Start Import</button>
                    </p>
                </div>
                
                <div class="wings-progress" id="import-progress" style="display: none;">
                    <h2>Import Progress</h2>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
                    </div>
                    <p id="progress-text">0 / 0 customers processed (0%)</p>
                    
                    <div class="import-stats">
                        <div class="stat">
                            <span class="stat-number" id="stat-total">0</span>
                            <span class="stat-label">Total</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="stat-created">0</span>
                            <span class="stat-label">Created</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="stat-updated">0</span>
                            <span class="stat-label">Updated</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="stat-errors">0</span>
                            <span class="stat-label">Errors</span>
                        </div>
                    </div>
                    
                    <div class="import-log">
                        <h3>Import Log</h3>
                        <div id="log-container" class="log-container">
                            <div class="log-entry">Ready to start import...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function manage_customers_page() {
        ?>
        <div class="wrap">
            <h1>Manage Wings Customers</h1>

            <!-- Check/Create Customer Section -->
            <div class="wings-check-create-section">
                <h2>Check/Create Customer in Wings</h2>
                <p>Check if a customer exists in Wings by company name (naziv) and optionally create them if they don't exist.</p>

                <form id="wings-check-create-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="check-naziv">Company Name (Naziv) <span class="required">*</span></label>
                            <input type="text" id="check-naziv" name="naziv" required />
                        </div>

                        <div class="form-group">
                            <label for="check-email">Email</label>
                            <input type="email" id="check-email" name="email" />
                        </div>

                        <div class="form-group">
                            <label for="check-kontakt">Contact Person</label>
                            <input type="text" id="check-kontakt" name="kontakt" />
                        </div>

                        <div class="form-group">
                            <label for="check-telefon">Phone</label>
                            <input type="text" id="check-telefon" name="telefon" />
                        </div>

                        <div class="form-group">
                            <label for="check-adresa">Address</label>
                            <input type="text" id="check-adresa" name="adresa" />
                        </div>

                        <div class="form-group">
                            <label for="check-mesto">City</label>
                            <input type="text" id="check-mesto" name="mesto" />
                        </div>

                        <div class="form-group">
                            <label for="check-pib">PIB</label>
                            <input type="text" id="check-pib" name="pib" />
                        </div>

                        <div class="form-group">
                            <label for="check-mb">MB</label>
                            <input type="text" id="check-mb" name="mb" />
                        </div>
                    </div>

                    <div class="form-actions">
                        <label class="checkbox-label">
                            <input type="checkbox" id="create-if-not-exists" />
                            Create customer in Wings if not found
                        </label>
                    </div>

                    <div class="button-group">
                        <button type="button" id="check-customer-btn" class="button button-primary">Check Customer</button>
                        <button type="button" id="clear-form-btn" class="button button-secondary">Clear Form</button>
                    </div>

                    <div id="check-result" style="display: none;"></div>
                </form>
            </div>

            <div class="wings-customers">
                <div class="customers-toolbar">
                    <input type="text" id="customer-search" placeholder="Search customers..." />
                    <button id="search-customers" class="button">Search</button>
                    <button id="refresh-customers" class="button">Refresh</button>
                </div>
                
                <table id="customers-table" class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Company Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>City</th>
                            <th>Wings ID</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="customers-tbody">
                        <tr>
                            <td colspan="7">Loading customers...</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="customers-pagination">
                    <button id="prev-page" class="button">Previous</button>
                    <span id="page-info">Page 1</span>
                    <button id="next-page" class="button">Next</button>
                </div>
            </div>
            
            <!-- Edit Modal -->
            <div id="edit-modal" class="wings-modal" style="display: none;">
                <div class="modal-content">
                    <h2>Edit Customer</h2>
                    <form id="edit-form">
                        <input type="hidden" id="edit-user-id" />
                        <table class="form-table">
                            <tr>
                                <th>Company Name:</th>
                                <td><input type="text" id="edit-company" class="regular-text" /></td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td><input type="email" id="edit-email" class="regular-text" /></td>
                            </tr>
                            <tr>
                                <th>Phone:</th>
                                <td><input type="text" id="edit-phone" class="regular-text" /></td>
                            </tr>
                            <tr>
                                <th>City:</th>
                                <td><input type="text" id="edit-city" class="regular-text" /></td>
                            </tr>
                        </table>
                        <p>
                            <button type="submit" class="button button-primary">Save</button>
                            <button type="button" class="button modal-close">Cancel</button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function settings_page() {
        if (isset($_POST['submit'])) {
            $settings = array(
                'api_url' => sanitize_url($_POST['api_url']),
                'api_alias' => sanitize_text_field($_POST['api_alias']),
                'api_username' => sanitize_text_field($_POST['api_username']),
                'api_password' => sanitize_text_field($_POST['api_password']),
                'batch_size' => intval($_POST['batch_size']),
                'default_role' => sanitize_text_field($_POST['default_role'])
            );
            update_option('wings_import_settings', $settings);
            echo '<div class="notice notice-success"><p>Settings saved!</p></div>';
        }
        
        $settings = get_option('wings_import_settings', array());
        ?>
        <div class="wrap">
            <h1>Wings Import Settings</h1>
            
            <form method="post">
                <table class="form-table">
                    <tr>
                        <th>API URL:</th>
                        <td><input type="url" name="api_url" value="<?php echo esc_attr($settings['api_url']); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th>Alias:</th>
                        <td><input type="text" name="api_alias" value="<?php echo esc_attr($settings['api_alias']); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th>Username:</th>
                        <td><input type="text" name="api_username" value="<?php echo esc_attr($settings['api_username']); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th>Password:</th>
                        <td><input type="password" name="api_password" value="<?php echo esc_attr($settings['api_password']); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th>Batch Size:</th>
                        <td><input type="number" name="batch_size" value="<?php echo esc_attr($settings['batch_size']); ?>" min="10" max="100" /></td>
                    </tr>
                    <tr>
                        <th>Default Role:</th>
                        <td>
                            <select name="default_role">
                                <?php
                                $roles = wp_roles()->get_names();
                                foreach ($roles as $role_key => $role_name) {
                                    $selected = selected($settings['default_role'], $role_key, false);
                                    echo "<option value='{$role_key}' {$selected}>{$role_name}</option>";
                                }
                                ?>
                            </select>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }

    // AJAX Handlers
    public function ajax_test_connection() {
        check_ajax_referer('wings_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $settings = get_option('wings_import_settings', array());
        $login_url = $settings['api_url'] . $settings['api_alias'] . '/system.user.log';

        $response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $settings['api_username'],
                'aUp' => $settings['api_password']
            ),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            wp_send_json_error('Connection failed: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code === 200) {
            wp_send_json_success('Connection successful!');
        } else {
            wp_send_json_error('Connection failed. Response code: ' . $response_code);
        }
    }

    public function ajax_import_customers() {
        check_ajax_referer('wings_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $settings = get_option('wings_import_settings', array());
        $start = intval($_POST['start'] ?? 0);
        $length = intval($_POST['length'] ?? 25);

        // Authenticate first
        $login_url = $settings['api_url'] . $settings['api_alias'] . '/system.user.log';
        $auth_response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $settings['api_username'],
                'aUp' => $settings['api_password']
            ),
            'timeout' => 30
        ));

        if (is_wp_error($auth_response)) {
            wp_send_json_error('Authentication failed: ' . $auth_response->get_error_message());
        }

        // Get session cookie
        $cookies = wp_remote_retrieve_cookies($auth_response);
        $session_cookie = '';
        foreach ($cookies as $cookie) {
            if ($cookie->name === 'PHPSESSID') {
                $session_cookie = $cookie->value;
                break;
            }
        }

        if (empty($session_cookie)) {
            wp_send_json_error('No session cookie received');
        }

        // Get customers from Wings API
        $api_url = $settings['api_url'] . $settings['api_alias'] . '/local.kupac.svi';
        $response = wp_remote_get($api_url . '?dStart=' . $start . '&dLength=' . $length . '&output=jsonapi', array(
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $session_cookie
            ),
            'timeout' => 60
        ));

        if (is_wp_error($response)) {
            wp_send_json_error('API request failed: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['data'])) {
            wp_send_json_error('Invalid API response');
        }

        $imported = 0;
        $created = 0;
        $updated = 0;
        $errors = 0;

        foreach ($data['data'] as $customer) {
            $result = $this->import_single_customer($customer);
            if ($result['success']) {
                $imported++;
                if ($result['action'] === 'created') {
                    $created++;
                } else {
                    $updated++;
                }
            } else {
                $errors++;
            }
        }

        wp_send_json_success(array(
            'imported' => $imported,
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors,
            'total' => $data['meta']['total'] ?? 0,
            'processed' => $start + count($data['data'])
        ));
    }

    private function import_single_customer($customer) {
        if (!isset($customer['attributes'])) {
            return array('success' => false, 'error' => 'Missing attributes');
        }

        $attrs = $customer['attributes'];
        $wings_id = $customer['id'] ?? '';

        // Generate email if missing
        $email = trim($attrs['email'] ?? '');
        if (empty($email)) {
            $naziv = trim($attrs['naziv'] ?? '');
            $clean_naziv = strtolower(preg_replace('/[^a-z0-9]/', '', $naziv));
            $email = substr($clean_naziv, 0, 30) . '@mail.com';
        }

        // Check if user exists
        $existing_user = null;
        if (!empty($wings_id)) {
            $users = get_users(array(
                'meta_key' => 'wings_customer_id',
                'meta_value' => $wings_id,
                'number' => 1
            ));
            if (!empty($users)) {
                $existing_user = $users[0];
            }
        }

        if (!$existing_user) {
            $existing_user = get_user_by('email', $email);
        }

        $settings = get_option('wings_import_settings', array());
        $user_data = array(
            'user_email' => $email,
            'display_name' => trim($attrs['naziv'] ?? ''),
            'first_name' => trim($attrs['kontakt'] ?? ''),
            'role' => $settings['default_role'] ?? 'Professionals'
        );

        if ($existing_user) {
            // Update existing user
            $user_data['ID'] = $existing_user->ID;
            $result = wp_update_user($user_data);
            $action = 'updated';
            $user_id = $existing_user->ID;
        } else {
            // Create new user
            $user_data['user_login'] = $this->generate_username($email, $wings_id);
            $user_data['user_pass'] = wp_generate_password(12, false);
            $result = wp_insert_user($user_data);
            $action = 'created';
            $user_id = $result;
        }

        if (is_wp_error($result)) {
            return array('success' => false, 'error' => $result->get_error_message());
        }

        // Update user meta
        update_user_meta($user_id, 'wings_customer_id', $wings_id);
        update_user_meta($user_id, 'billing_phone', trim($attrs['telefon'] ?? ''));
        update_user_meta($user_id, 'billing_city', $this->extract_city($attrs['mesto'] ?? ''));
        update_user_meta($user_id, 'wings_pib', trim($attrs['pib'] ?? ''));
        update_user_meta($user_id, 'wings_status', trim($attrs['status'] ?? 'K'));

        return array('success' => true, 'action' => $action, 'user_id' => $user_id);
    }

    private function generate_username($email, $wings_id) {
        $base = strstr($email, '@', true);
        if (username_exists($base)) {
            $base = $base . '_' . $wings_id;
        }

        $counter = 1;
        $username = $base;
        while (username_exists($username)) {
            $username = $base . '_' . $counter;
            $counter++;
        }

        return $username;
    }

    private function extract_city($mesto) {
        if (preg_match('/^\d{5}\s+(.+)$/', trim($mesto), $matches)) {
            return trim($matches[1]);
        }
        return trim($mesto);
    }

    public function ajax_get_customers() {
        check_ajax_referer('wings_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $page = intval($_POST['page'] ?? 1);
        $search = sanitize_text_field($_POST['search'] ?? '');

        // First try to get users with wings_customer_id
        $args = array(
            'meta_query' => array(
                array(
                    'key' => 'wings_customer_id',
                    'compare' => 'EXISTS'
                )
            ),
            'number' => 20,
            'offset' => ($page - 1) * 20
        );

        if (!empty($search)) {
            $args['search'] = '*' . $search . '*';
        }

        $users = get_users($args);

        // If no users found with wings_customer_id, try to find users with other Wings meta
        if (empty($users)) {
            $args['meta_query'] = array(
                'relation' => 'OR',
                array(
                    'key' => 'wings_customer_id',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key' => 'wings_pib',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key' => 'wings_status',
                    'compare' => 'EXISTS'
                )
            );
            $users = get_users($args);
        }

        // If still no users, get all users (might be imported without proper meta)
        if (empty($users)) {
            unset($args['meta_query']);
            $args['role__in'] = array('Professionals', 'customer', 'subscriber'); // Common roles for imported customers
            $users = get_users($args);
        }

        $customers = array();

        foreach ($users as $user) {
            $wings_id = get_user_meta($user->ID, 'wings_customer_id', true);
            $wings_status = get_user_meta($user->ID, 'wings_status', true);

            $customers[] = array(
                'id' => $user->ID,
                'display_name' => $user->display_name,
                'user_email' => $user->user_email,
                'billing_phone' => get_user_meta($user->ID, 'billing_phone', true),
                'billing_city' => get_user_meta($user->ID, 'billing_city', true),
                'wings_customer_id' => $wings_id,
                'wings_status' => $wings_status ?: 'Unknown'
            );
        }

        wp_send_json_success($customers);
    }

    public function ajax_update_customer() {
        check_ajax_referer('wings_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $user_id = intval($_POST['user_id']);
        $data = $_POST['data'] ?? array();

        $user_data = array('ID' => $user_id);
        if (isset($data['display_name'])) {
            $user_data['display_name'] = sanitize_text_field($data['display_name']);
        }
        if (isset($data['user_email'])) {
            $user_data['user_email'] = sanitize_email($data['user_email']);
        }

        $result = wp_update_user($user_data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        // Update meta fields
        if (isset($data['billing_phone'])) {
            update_user_meta($user_id, 'billing_phone', sanitize_text_field($data['billing_phone']));
        }
        if (isset($data['billing_city'])) {
            update_user_meta($user_id, 'billing_city', sanitize_text_field($data['billing_city']));
        }

        wp_send_json_success('Customer updated successfully');
    }

    public function ajax_delete_customer() {
        check_ajax_referer('wings_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $user_id = intval($_POST['user_id']);

        // Verify this is a Wings customer
        $wings_id = get_user_meta($user_id, 'wings_customer_id', true);
        if (empty($wings_id)) {
            wp_send_json_error('Not a Wings customer');
        }

        $result = wp_delete_user($user_id);

        if (!$result) {
            wp_send_json_error('Failed to delete customer');
        }

        wp_send_json_success('Customer deleted successfully');
    }

    public function ajax_check_create_customer() {
        check_ajax_referer('wings_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $naziv = sanitize_text_field($_POST['naziv'] ?? '');
        $create_if_not_exists = isset($_POST['create_if_not_exists']) && $_POST['create_if_not_exists'] === 'true';

        if (empty($naziv)) {
            wp_send_json_error('Company name (naziv) is required.');
        }

        $settings = get_option('wings_import_settings', array());

        // Check if customer exists
        $existing_customer = $this->find_customer_by_naziv($naziv, $settings);

        if (is_wp_error($existing_customer)) {
            wp_send_json_error($existing_customer->get_error_message());
        }

        if ($existing_customer) {
            // Customer exists
            wp_send_json_success(array(
                'exists' => true,
                'customer' => $existing_customer,
                'message' => sprintf('Customer "%s" already exists in Wings.', $naziv)
            ));
        } else {
            // Customer doesn't exist
            if ($create_if_not_exists) {
                // Create customer with provided data
                $customer_data = array(
                    'naziv' => $naziv,
                    'adresa' => sanitize_text_field($_POST['adresa'] ?? ''),
                    'mesto' => sanitize_text_field($_POST['mesto'] ?? ''),
                    'kontakt' => sanitize_text_field($_POST['kontakt'] ?? ''),
                    'telefon' => sanitize_text_field($_POST['telefon'] ?? ''),
                    'email' => sanitize_email($_POST['email'] ?? ''),
                    'pib' => sanitize_text_field($_POST['pib'] ?? ''),
                    'mb' => sanitize_text_field($_POST['mb'] ?? ''),
                    'komercijalista' => '1',
                    'klasa' => '1'
                );

                $create_result = $this->create_customer_in_wings($customer_data, $settings);

                if (is_wp_error($create_result)) {
                    wp_send_json_error($create_result->get_error_message());
                }

                wp_send_json_success(array(
                    'exists' => false,
                    'created' => true,
                    'customer' => $create_result,
                    'message' => sprintf('Customer "%s" created successfully in Wings.', $naziv)
                ));
            } else {
                wp_send_json_success(array(
                    'exists' => false,
                    'created' => false,
                    'message' => sprintf('Customer "%s" does not exist in Wings.', $naziv)
                ));
            }
        }
    }

    private function find_customer_by_naziv($naziv, $settings) {
        // Authenticate first
        $login_url = $settings['api_url'] . $settings['api_alias'] . '/system.user.log';
        $auth_response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $settings['api_username'],
                'aUp' => $settings['api_password']
            ),
            'timeout' => 30
        ));

        if (is_wp_error($auth_response)) {
            return new WP_Error('auth_failed', 'Authentication failed: ' . $auth_response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($auth_response);
        if ($response_code !== 200) {
            return new WP_Error('auth_failed', 'Authentication failed. Response code: ' . $response_code);
        }

        // Get session cookie
        $cookies = wp_remote_retrieve_cookies($auth_response);
        $session_cookie = '';
        foreach ($cookies as $cookie) {
            if ($cookie->name === 'PHPSESSID') {
                $session_cookie = $cookie->value;
                break;
            }
        }

        if (empty($session_cookie)) {
            return new WP_Error('no_session', 'No session cookie received');
        }

        // Search customers by naziv
        $api_url = $settings['api_url'] . $settings['api_alias'] . '/local.kupac.svi';

        // Try different search parameter formats
        $search_params = array(
            'dStart' => 0,
            'dLength' => 100,
            'output' => 'jsonapi'
        );

        // Add search parameter - try both formats
        if (!empty($naziv)) {
            $search_params['search[value]'] = $naziv;
            $search_params['search[regex]'] = 'false';
        }

        $search_url = $api_url . '?' . http_build_query($search_params);

        // Debug: Log the search request
        error_log('Wings API Search Request URL: ' . $search_url);

        $response = wp_remote_get($search_url, array(
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $session_cookie
            ),
            'timeout' => 60
        ));

        if (is_wp_error($response)) {
            return new WP_Error('api_error', 'API request failed: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error('api_error', 'API request failed. Response code: ' . $response_code);
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            return false;
        }

        // Look for exact match (case-insensitive)
        foreach ($data['data'] as $customer) {
            if (isset($customer['attributes']['naziv']) &&
                trim(strtolower($customer['attributes']['naziv'])) === trim(strtolower($naziv))) {
                return $customer;
            }
        }

        return false;
    }

    private function create_customer_in_wings($customer_data, $settings) {
        // Authenticate first
        $login_url = $settings['api_url'] . $settings['api_alias'] . '/system.user.log';
        $auth_response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $settings['api_username'],
                'aUp' => $settings['api_password']
            ),
            'timeout' => 30
        ));

        if (is_wp_error($auth_response)) {
            return new WP_Error('auth_failed', 'Authentication failed: ' . $auth_response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($auth_response);
        if ($response_code !== 200) {
            return new WP_Error('auth_failed', 'Authentication failed. Response code: ' . $response_code);
        }

        // Get session cookie
        $cookies = wp_remote_retrieve_cookies($auth_response);
        $session_cookie = '';
        foreach ($cookies as $cookie) {
            if ($cookie->name === 'PHPSESSID') {
                $session_cookie = $cookie->value;
                break;
            }
        }

        if (empty($session_cookie)) {
            return new WP_Error('no_session', 'No session cookie received');
        }

        // Create customer
        $api_url = $settings['api_url'] . $settings['api_alias'] . '/local.kupac.nov';

        // Debug: Log the request details
        error_log('Wings API Create Customer Request:');
        error_log('URL: ' . $api_url);
        error_log('Data: ' . print_r($customer_data, true));

        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $session_cookie,
                'Content-Type' => 'application/x-www-form-urlencoded'
            ),
            'body' => $customer_data,
            'timeout' => 60
        ));

        if (is_wp_error($response)) {
            error_log('Wings API Create Customer Error: ' . $response->get_error_message());
            return new WP_Error('create_failed', 'Customer creation failed: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        // Debug: Log the response
        error_log('Wings API Create Customer Response Code: ' . $response_code);
        error_log('Wings API Create Customer Response Body: ' . $body);

        if ($response_code !== 200) {
            return new WP_Error('create_failed', 'Customer creation failed. Response code: ' . $response_code . '. Response: ' . $body);
        }

        // Try to parse JSON response, but also handle plain text responses
        $result = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // If not JSON, return the raw response
            $result = array('response' => $body, 'success' => true);
        }

        return $result;
    }
}

// Initialize the plugin
Wings_Customer_Import::get_instance();
