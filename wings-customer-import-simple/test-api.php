<?php
/**
 * Test Wings API Customer Creation
 * 
 * Access this file directly in browser: /wp-content/plugins/wings-customer-import-simple/test-api.php
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Wings API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .button { background: #0073aa; color: white; padding: 8px 15px; border: none; cursor: pointer; margin: 5px; }
        .button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>Wings API Test</h1>
    
    <?php
    $settings = get_option('wings_import_settings', array());
    
    if (isset($_POST['test_connection'])) {
        echo "<div class='section'>";
        echo "<h2>Testing API Connection</h2>";
        
        $login_url = $settings['api_url'] . $settings['api_alias'] . '/system.user.log';
        echo "<p class='info'>Testing login to: {$login_url}</p>";
        
        $auth_response = wp_remote_post($login_url, array(
            'body' => array(
                'aUn' => $settings['api_username'],
                'aUp' => $settings['api_password']
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($auth_response)) {
            echo "<p class='error'>Authentication failed: " . $auth_response->get_error_message() . "</p>";
        } else {
            $response_code = wp_remote_retrieve_response_code($auth_response);
            echo "<p>Response code: {$response_code}</p>";
            
            if ($response_code === 200) {
                echo "<p class='success'>✅ Authentication successful!</p>";
                
                // Get session cookie
                $cookies = wp_remote_retrieve_cookies($auth_response);
                $session_cookie = '';
                foreach ($cookies as $cookie) {
                    if ($cookie->name === 'PHPSESSID') {
                        $session_cookie = $cookie->value;
                        break;
                    }
                }
                
                if (!empty($session_cookie)) {
                    echo "<p class='success'>✅ Session cookie obtained</p>";
                    $_SESSION['wings_session'] = $session_cookie;
                } else {
                    echo "<p class='error'>❌ No session cookie received</p>";
                }
            } else {
                echo "<p class='error'>❌ Authentication failed with code: {$response_code}</p>";
                $body = wp_remote_retrieve_body($auth_response);
                echo "<pre>" . htmlspecialchars($body) . "</pre>";
            }
        }
        echo "</div>";
    }
    
    if (isset($_POST['test_create']) && !empty($_SESSION['wings_session'])) {
        echo "<div class='section'>";
        echo "<h2>Testing Customer Creation</h2>";
        
        $test_customer_data = array(
            'naziv' => 'Test Company ' . date('Y-m-d H:i:s'),
            'adresa' => 'Test Address 123',
            'mesto' => 'Test City',
            'kontakt' => 'Test Contact',
            'telefon' => '+381*********',
            'email' => '<EMAIL>',
            'pib' => '*********',
            'mb' => '*********',
            'komercijalista' => '1',
            'klasa' => '1'
        );
        
        echo "<p>Attempting to create customer with data:</p>";
        echo "<pre>" . print_r($test_customer_data, true) . "</pre>";
        
        $create_url = $settings['api_url'] . $settings['api_alias'] . '/local.kupac.nov';
        echo "<p class='info'>Create URL: {$create_url}</p>";
        
        $create_response = wp_remote_post($create_url, array(
            'headers' => array(
                'Cookie' => 'PHPSESSID=' . $_SESSION['wings_session'],
                'Content-Type' => 'application/x-www-form-urlencoded'
            ),
            'body' => $test_customer_data,
            'timeout' => 60
        ));
        
        if (is_wp_error($create_response)) {
            echo "<p class='error'>❌ Customer creation failed: " . $create_response->get_error_message() . "</p>";
        } else {
            $create_code = wp_remote_retrieve_response_code($create_response);
            $create_body = wp_remote_retrieve_body($create_response);
            echo "<p>Create response code: {$create_code}</p>";
            echo "<p>Create response body:</p>";
            echo "<pre>" . htmlspecialchars($create_body) . "</pre>";
            
            if ($create_code === 200) {
                echo "<p class='success'>✅ Customer creation appears successful!</p>";
            } else {
                echo "<p class='error'>❌ Customer creation returned non-200 code</p>";
            }
        }
        echo "</div>";
    }
    
    if (isset($_POST['check_users'])) {
        echo "<div class='section'>";
        echo "<h2>Checking WordPress Users</h2>";
        
        // Check users with wings_customer_id
        $users_with_wings = get_users(array(
            'meta_query' => array(
                array(
                    'key' => 'wings_customer_id',
                    'compare' => 'EXISTS'
                )
            ),
            'number' => 10
        ));
        
        echo "<p>Found " . count($users_with_wings) . " users with wings_customer_id:</p>";
        foreach ($users_with_wings as $user) {
            $wings_id = get_user_meta($user->ID, 'wings_customer_id', true);
            echo "<li>User: {$user->display_name} ({$user->user_email}) - Wings ID: {$wings_id}</li>";
        }
        
        // Check all users
        $all_users = get_users(array('number' => 5));
        echo "<p>Sample of all users (first 5):</p>";
        foreach ($all_users as $user) {
            echo "<li>User: {$user->display_name} ({$user->user_email}) - Role: " . implode(', ', $user->roles) . "</li>";
        }
        
        echo "</div>";
    }
    ?>
    
    <div class="section">
        <h2>Current Settings</h2>
        <pre><?php print_r($settings); ?></pre>
    </div>
    
    <div class="section">
        <h2>Tests</h2>
        <form method="post">
            <button type="submit" name="test_connection" class="button">Test API Connection</button>
            <button type="submit" name="test_create" class="button">Test Customer Creation</button>
            <button type="submit" name="check_users" class="button">Check WordPress Users</button>
        </form>
    </div>
    
    <div class="section">
        <h2>Manual Customer Creation Test</h2>
        <form method="post">
            <table>
                <tr>
                    <td>Company Name:</td>
                    <td><input type="text" name="manual_naziv" value="Test Manual Company" required /></td>
                </tr>
                <tr>
                    <td>Email:</td>
                    <td><input type="email" name="manual_email" value="<EMAIL>" /></td>
                </tr>
                <tr>
                    <td>Contact:</td>
                    <td><input type="text" name="manual_kontakt" value="Manual Contact" /></td>
                </tr>
                <tr>
                    <td>Phone:</td>
                    <td><input type="text" name="manual_telefon" value="+381*********" /></td>
                </tr>
                <tr>
                    <td>Address:</td>
                    <td><input type="text" name="manual_adresa" value="Manual Address 123" /></td>
                </tr>
                <tr>
                    <td>City:</td>
                    <td><input type="text" name="manual_mesto" value="Manual City" /></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <button type="submit" name="manual_create" class="button">Create Manual Customer</button>
                    </td>
                </tr>
            </table>
        </form>
        
        <?php
        if (isset($_POST['manual_create'])) {
            echo "<h3>Manual Customer Creation Result</h3>";
            
            // Use the plugin's method
            $plugin = Wings_Customer_Import::get_instance();
            
            $customer_data = array(
                'naziv' => sanitize_text_field($_POST['manual_naziv']),
                'email' => sanitize_email($_POST['manual_email']),
                'kontakt' => sanitize_text_field($_POST['manual_kontakt']),
                'telefon' => sanitize_text_field($_POST['manual_telefon']),
                'adresa' => sanitize_text_field($_POST['manual_adresa']),
                'mesto' => sanitize_text_field($_POST['manual_mesto']),
                'pib' => '',
                'mb' => '',
                'komercijalista' => '1',
                'klasa' => '1'
            );
            
            // Use reflection to call private method
            $reflection = new ReflectionClass($plugin);
            $method = $reflection->getMethod('create_customer_in_wings');
            $method->setAccessible(true);
            
            $result = $method->invoke($plugin, $customer_data, $settings);
            
            if (is_wp_error($result)) {
                echo "<p class='error'>❌ Creation failed: " . $result->get_error_message() . "</p>";
            } else {
                echo "<p class='success'>✅ Customer created successfully!</p>";
                echo "<pre>" . print_r($result, true) . "</pre>";
            }
        }
        ?>
    </div>
    
</body>
</html>
