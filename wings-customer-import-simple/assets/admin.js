/* Wings Customer Import - Simple Admin JavaScript */

jQuery(document).ready(function($) {
    
    let isImporting = false;
    let currentPage = 1;
    
    // Dashboard functionality
    $('#test-connection').on('click', testConnection);
    $('#start-import').on('click', startImport);
    
    // Customer management
    $('#search-customers').on('click', searchCustomers);
    $('#refresh-customers').on('click', loadCustomers);
    $('#prev-page').on('click', prevPage);
    $('#next-page').on('click', nextPage);
    
    // Modal controls
    $('.modal-close').on('click', closeModal);
    $(document).on('click', '.edit-customer', editCustomer);
    $(document).on('click', '.delete-customer', deleteCustomer);
    $('#edit-form').on('submit', saveCustomer);
    
    // Load customers if on manage page
    if ($('#customers-table').length > 0) {
        loadCustomers();
    }

    // Check/Create customer functionality
    $('#check-customer-btn').on('click', checkCreateCustomer);
    $('#clear-form-btn').on('click', clearCheckForm);
    
    // Test connection
    function testConnection() {
        const $btn = $('#test-connection');
        $btn.addClass('loading').prop('disabled', true);
        
        $.post(wingsAjax.ajaxUrl, {
            action: 'wings_test_connection',
            nonce: wingsAjax.nonce
        })
        .done(function(response) {
            if (response.success) {
                showNotice('✅ ' + response.data, 'success');
            } else {
                showNotice('❌ ' + response.data, 'error');
            }
        })
        .fail(function() {
            showNotice('❌ Network error', 'error');
        })
        .always(function() {
            $btn.removeClass('loading').prop('disabled', false);
        });
    }
    
    // Start import
    function startImport() {
        if (isImporting) return;
        
        isImporting = true;
        $('#import-progress').show();
        $('#start-import').prop('disabled', true);
        
        addLogEntry('🚀 Starting Wings customer import...');
        
        processImportBatch(0);
    }
    
    // Process import batch
    function processImportBatch(start) {
        if (!isImporting) return;
        
        addLogEntry(`📥 Processing batch starting at ${start}...`);
        
        $.post(wingsAjax.ajaxUrl, {
            action: 'wings_import_customers',
            nonce: wingsAjax.nonce,
            start: start,
            length: 25
        })
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                
                // Update stats
                $('#stat-total').text(data.total);
                $('#stat-created').text(data.created);
                $('#stat-updated').text(data.updated);
                $('#stat-errors').text(data.errors);
                
                // Update progress
                const percentage = data.total > 0 ? Math.round((data.processed / data.total) * 100) : 0;
                $('#progress-fill').css('width', percentage + '%');
                $('#progress-text').text(`${data.processed} / ${data.total} customers processed (${percentage}%)`);
                
                addLogEntry(`✅ Batch completed: ${data.imported} imported (${data.created} created, ${data.updated} updated, ${data.errors} errors)`);
                
                // Continue if not finished
                if (data.processed < data.total) {
                    setTimeout(() => processImportBatch(data.processed), 1000);
                } else {
                    addLogEntry('🎉 Import completed successfully!');
                    isImporting = false;
                    $('#start-import').prop('disabled', false);
                }
            } else {
                addLogEntry('❌ Import error: ' + response.data);
                isImporting = false;
                $('#start-import').prop('disabled', false);
            }
        })
        .fail(function() {
            addLogEntry('❌ Network error during import');
            isImporting = false;
            $('#start-import').prop('disabled', false);
        });
    }
    
    // Add log entry
    function addLogEntry(message) {
        const timestamp = new Date().toLocaleTimeString();
        const entry = `<div class="log-entry">[${timestamp}] ${message}</div>`;
        $('#log-container').append(entry);
        $('#log-container').scrollTop($('#log-container')[0].scrollHeight);
    }
    
    // Load customers
    function loadCustomers() {
        $('#customers-tbody').html('<tr><td colspan="7">Loading customers...</td></tr>');
        
        const search = $('#customer-search').val();
        
        $.post(wingsAjax.ajaxUrl, {
            action: 'wings_get_customers',
            nonce: wingsAjax.nonce,
            page: currentPage,
            search: search
        })
        .done(function(response) {
            if (response.success) {
                renderCustomers(response.data);
            } else {
                $('#customers-tbody').html('<tr><td colspan="7">Error loading customers</td></tr>');
            }
        })
        .fail(function() {
            $('#customers-tbody').html('<tr><td colspan="7">Network error</td></tr>');
        });
    }
    
    // Render customers table
    function renderCustomers(customers) {
        const tbody = $('#customers-tbody');
        tbody.empty();
        
        if (customers.length === 0) {
            tbody.html('<tr><td colspan="7">No customers found</td></tr>');
            return;
        }
        
        customers.forEach(function(customer) {
            const statusClass = customer.wings_status === 'K' ? 'status-active' : 'status-inactive';
            const statusText = customer.wings_status === 'K' ? 'Active' : 'Inactive';
            
            const row = `
                <tr>
                    <td><strong>${escapeHtml(customer.display_name)}</strong></td>
                    <td>${escapeHtml(customer.user_email)}</td>
                    <td>${escapeHtml(customer.billing_phone || '-')}</td>
                    <td>${escapeHtml(customer.billing_city || '-')}</td>
                    <td>${escapeHtml(customer.wings_customer_id || '-')}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>
                        <button class="button button-small edit-customer" data-id="${customer.id}">Edit</button>
                        <button class="button button-small delete-customer" data-id="${customer.id}" data-name="${escapeHtml(customer.display_name)}">Delete</button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
        
        updatePagination();
    }
    
    // Search customers
    function searchCustomers() {
        currentPage = 1;
        loadCustomers();
    }
    
    // Pagination
    function prevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadCustomers();
        }
    }
    
    function nextPage() {
        currentPage++;
        loadCustomers();
    }
    
    function updatePagination() {
        $('#page-info').text('Page ' + currentPage);
        $('#prev-page').prop('disabled', currentPage <= 1);
    }
    
    // Edit customer
    function editCustomer() {
        const customerId = $(this).data('id');
        
        // Find customer data from current table
        const row = $(this).closest('tr');
        const customerName = row.find('td:first strong').text();
        const customerEmail = row.find('td:nth-child(2)').text();
        const customerPhone = row.find('td:nth-child(3)').text();
        const customerCity = row.find('td:nth-child(4)').text();
        
        // Populate form
        $('#edit-user-id').val(customerId);
        $('#edit-company').val(customerName);
        $('#edit-email').val(customerEmail);
        $('#edit-phone').val(customerPhone === '-' ? '' : customerPhone);
        $('#edit-city').val(customerCity === '-' ? '' : customerCity);
        
        $('#edit-modal').show();
    }
    
    // Save customer
    function saveCustomer(e) {
        e.preventDefault();
        
        const $form = $('#edit-form');
        const $btn = $form.find('button[type="submit"]');
        
        $btn.addClass('loading').prop('disabled', true);
        
        $.post(wingsAjax.ajaxUrl, {
            action: 'wings_update_customer',
            nonce: wingsAjax.nonce,
            user_id: $('#edit-user-id').val(),
            data: {
                display_name: $('#edit-company').val(),
                user_email: $('#edit-email').val(),
                billing_phone: $('#edit-phone').val(),
                billing_city: $('#edit-city').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                closeModal();
                showNotice('✅ Customer updated successfully', 'success');
                loadCustomers();
            } else {
                showNotice('❌ ' + response.data, 'error');
            }
        })
        .fail(function() {
            showNotice('❌ Network error', 'error');
        })
        .always(function() {
            $btn.removeClass('loading').prop('disabled', false);
        });
    }
    
    // Delete customer
    function deleteCustomer() {
        const customerId = $(this).data('id');
        const customerName = $(this).data('name');
        
        if (!confirm(`Are you sure you want to delete "${customerName}"?\n\nThis action cannot be undone.`)) {
            return;
        }
        
        const $btn = $(this);
        $btn.addClass('loading').prop('disabled', true);
        
        $.post(wingsAjax.ajaxUrl, {
            action: 'wings_delete_customer',
            nonce: wingsAjax.nonce,
            user_id: customerId
        })
        .done(function(response) {
            if (response.success) {
                showNotice('✅ Customer deleted successfully', 'success');
                loadCustomers();
            } else {
                showNotice('❌ ' + response.data, 'error');
            }
        })
        .fail(function() {
            showNotice('❌ Network error', 'error');
        })
        .always(function() {
            $btn.removeClass('loading').prop('disabled', false);
        });
    }
    
    // Close modal
    function closeModal() {
        $('.wings-modal').hide();
    }
    
    // Show notice
    function showNotice(message, type) {
        const notice = `<div class="notice notice-${type} is-dismissible"><p>${message}</p></div>`;
        $('.wrap h1').after(notice);
        
        setTimeout(function() {
            $('.notice').fadeOut();
        }, 5000);
    }
    
    // Check/Create Customer functionality
    function checkCreateCustomer() {
        const $btn = $('#check-customer-btn');
        const $result = $('#check-result');

        const naziv = $('#check-naziv').val().trim();
        if (!naziv) {
            showNotice('Company name (naziv) is required.', 'error');
            return;
        }

        const createIfNotExists = $('#create-if-not-exists').is(':checked');

        // Prepare form data
        const formData = {
            action: 'wings_check_create_customer',
            nonce: wingsAjax.nonce,
            naziv: naziv,
            create_if_not_exists: createIfNotExists,
            email: $('#check-email').val().trim(),
            kontakt: $('#check-kontakt').val().trim(),
            telefon: $('#check-telefon').val().trim(),
            adresa: $('#check-adresa').val().trim(),
            mesto: $('#check-mesto').val().trim(),
            pib: $('#check-pib').val().trim(),
            mb: $('#check-mb').val().trim()
        };

        // Show loading state
        $btn.prop('disabled', true).text('Checking...');
        $result.hide();

        $.post(wingsAjax.ajaxUrl, formData)
            .done(function(response) {
                if (response.success) {
                    const data = response.data;
                    let resultHtml = '';
                    let resultClass = '';

                    if (data.exists) {
                        resultClass = 'notice-info';
                        resultHtml = `
                            <div class="notice ${resultClass}">
                                <p><strong>${data.message}</strong></p>
                                <div style="margin-top: 10px;">
                                    <strong>Customer Details:</strong><br>
                                    <strong>ID:</strong> ${data.customer.id || 'N/A'}<br>
                                    <strong>Name:</strong> ${data.customer.attributes?.naziv || 'N/A'}<br>
                                    <strong>Email:</strong> ${data.customer.attributes?.email || 'N/A'}<br>
                                    <strong>Contact:</strong> ${data.customer.attributes?.kontakt || 'N/A'}<br>
                                    <strong>Phone:</strong> ${data.customer.attributes?.telefon || 'N/A'}<br>
                                    <strong>Address:</strong> ${data.customer.attributes?.adresa || 'N/A'}<br>
                                    <strong>City:</strong> ${data.customer.attributes?.mesto || 'N/A'}
                                </div>
                            </div>
                        `;
                    } else if (data.created) {
                        resultClass = 'notice-success';
                        resultHtml = `
                            <div class="notice ${resultClass}">
                                <p><strong>${data.message}</strong></p>
                                <div style="margin-top: 10px;">
                                    <strong>New Customer Created:</strong><br>
                                    Customer has been successfully created in Wings.
                                </div>
                            </div>
                        `;
                    } else {
                        resultClass = 'notice-warning';
                        resultHtml = `
                            <div class="notice ${resultClass}">
                                <p><strong>${data.message}</strong></p>
                                <p>You can check the "Create customer in Wings if not found" option and try again to create this customer.</p>
                            </div>
                        `;
                    }

                    $result.html(resultHtml).show();

                    // If customer was created, optionally clear the form
                    if (data.created) {
                        setTimeout(() => {
                            if (confirm('Customer created successfully! Would you like to clear the form for a new search?')) {
                                clearCheckForm();
                            }
                        }, 1000);
                    }
                } else {
                    showNotice(response.data || 'An error occurred while checking the customer.', 'error');
                }
            })
            .fail(function(xhr, status, error) {
                showNotice('Request failed: ' + error, 'error');
            })
            .always(function() {
                $btn.prop('disabled', false).text('Check Customer');
            });
    }

    function clearCheckForm() {
        $('#wings-check-create-form')[0].reset();
        $('#create-if-not-exists').prop('checked', false);
        $('#check-result').hide();
        $('#check-naziv').focus();
    }

    // Escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
});
