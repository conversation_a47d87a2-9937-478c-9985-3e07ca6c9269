/* Wings Customer Import - Simple Admin Styles */

.wings-dashboard {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

.wings-config {
    margin-bottom: 30px;
}

.wings-progress {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background: #e0e0e0;
    border-radius: 15px;
    overflow: hidden;
    margin: 15px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
    border-radius: 15px;
}

.import-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #007cba;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007cba;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    margin-top: 5px;
}

.import-log {
    margin-top: 20px;
}

.log-container {
    height: 200px;
    overflow-y: auto;
    background: #1e1e1e;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    padding: 15px;
    border-radius: 5px;
    font-size: 12px;
    line-height: 1.4;
}

.log-entry {
    margin: 2px 0;
    word-wrap: break-word;
}

/* Customer Management */
.wings-customers {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

.customers-toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.customers-toolbar input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 300px;
}

.customers-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Modal Styles */
.wings-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 20px;
}

.modal-content h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Loading */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .customers-toolbar {
        flex-direction: column;
    }
    
    .customers-toolbar input {
        width: 100%;
    }
    
    .import-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .import-stats {
        grid-template-columns: 1fr;
    }
    
    .customers-pagination {
        flex-direction: column;
        gap: 10px;
    }
}

/* Buttons */
.button.loading {
    position: relative;
    color: transparent;
}

.button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Success/Error Messages */
.notice {
    margin: 15px 0;
    padding: 12px;
    border-left: 4px solid;
    border-radius: 4px;
}

.notice-success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.notice-error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

/* Table Improvements */
.wp-list-table th,
.wp-list-table td {
    padding: 12px 8px;
}

.wp-list-table .button {
    margin: 0 2px;
    padding: 4px 8px;
    font-size: 12px;
}

/* Form Improvements */
.form-table th {
    width: 150px;
    font-weight: 600;
}

.form-table input,
.form-table select {
    width: 100%;
    max-width: 400px;
}

/* Check/Create Customer Section */
.wings-check-create-section {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.wings-check-create-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #23282d;
    font-size: 18px;
}

.wings-check-create-section p {
    margin-bottom: 20px;
    color: #666;
}

#wings-check-create-form .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

#wings-check-create-form .form-group {
    display: flex;
    flex-direction: column;
}

#wings-check-create-form .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

#wings-check-create-form .form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

#wings-check-create-form .form-group input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
    outline: none;
}

.required {
    color: red;
}

.form-actions {
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
    margin: 0;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

#check-result {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

#check-result .notice {
    margin: 0;
    padding: 12px;
    border-left: 4px solid;
    background: #fff;
    border-radius: 4px;
}

#check-result .notice-success {
    border-left-color: #00a32a;
    background-color: #f0f6fc;
}

#check-result .notice-info {
    border-left-color: #72aee6;
    background-color: #f0f6fc;
}

#check-result .notice-warning {
    border-left-color: #dba617;
    background-color: #fcf9e8;
}

#check-result .notice-error {
    border-left-color: #d63638;
    background-color: #fcf0f1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #wings-check-create-form .form-grid {
        grid-template-columns: 1fr;
    }

    .button-group {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions {
        text-align: left;
    }
}
