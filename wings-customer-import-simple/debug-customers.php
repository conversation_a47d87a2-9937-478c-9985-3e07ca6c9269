<?php
/**
 * Debug script for Wings Customer Import - Simple
 * 
 * This script helps debug customer display and creation issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing outside WordPress, include WordPress
    require_once('../../../wp-config.php');
}

echo "<h1>Wings Customer Import Debug</h1>";

// 1. Check if any users have wings_customer_id meta
echo "<h2>1. Checking for users with wings_customer_id meta</h2>";
$users_with_wings_id = get_users(array(
    'meta_query' => array(
        array(
            'key' => 'wings_customer_id',
            'compare' => 'EXISTS'
        )
    ),
    'number' => 10
));

echo "<p>Found " . count($users_with_wings_id) . " users with wings_customer_id meta:</p>";
foreach ($users_with_wings_id as $user) {
    $wings_id = get_user_meta($user->ID, 'wings_customer_id', true);
    echo "<li>User ID: {$user->ID}, Email: {$user->user_email}, Display Name: {$user->display_name}, Wings ID: {$wings_id}</li>";
}

// 2. Check all users and their meta
echo "<h2>2. Checking all users and their meta (first 10)</h2>";
$all_users = get_users(array('number' => 10));
echo "<p>Total users found: " . count($all_users) . "</p>";
foreach ($all_users as $user) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>";
    echo "<strong>User ID:</strong> {$user->ID}<br>";
    echo "<strong>Email:</strong> {$user->user_email}<br>";
    echo "<strong>Display Name:</strong> {$user->display_name}<br>";
    echo "<strong>Role:</strong> " . implode(', ', $user->roles) . "<br>";
    
    // Check for Wings-related meta
    $meta_keys = array('wings_customer_id', 'wings_pib', 'wings_status', 'billing_phone', 'billing_city');
    foreach ($meta_keys as $key) {
        $value = get_user_meta($user->ID, $key, true);
        if (!empty($value)) {
            echo "<strong>{$key}:</strong> {$value}<br>";
        }
    }
    echo "</div>";
}

// 3. Test Wings API connection
echo "<h2>3. Testing Wings API Connection</h2>";
$settings = get_option('wings_import_settings', array());
echo "<p>Current settings:</p>";
echo "<pre>" . print_r($settings, true) . "</pre>";

if (!empty($settings['api_url']) && !empty($settings['api_alias'])) {
    $login_url = $settings['api_url'] . $settings['api_alias'] . '/system.user.log';
    echo "<p>Testing login to: {$login_url}</p>";
    
    $auth_response = wp_remote_post($login_url, array(
        'body' => array(
            'aUn' => $settings['api_username'],
            'aUp' => $settings['api_password']
        ),
        'timeout' => 30
    ));
    
    if (is_wp_error($auth_response)) {
        echo "<p style='color: red;'>Authentication failed: " . $auth_response->get_error_message() . "</p>";
    } else {
        $response_code = wp_remote_retrieve_response_code($auth_response);
        echo "<p>Response code: {$response_code}</p>";
        
        if ($response_code === 200) {
            echo "<p style='color: green;'>Authentication successful!</p>";
            
            // Get session cookie
            $cookies = wp_remote_retrieve_cookies($auth_response);
            $session_cookie = '';
            foreach ($cookies as $cookie) {
                if ($cookie->name === 'PHPSESSID') {
                    $session_cookie = $cookie->value;
                    break;
                }
            }
            
            if (!empty($session_cookie)) {
                echo "<p>Session cookie obtained: " . substr($session_cookie, 0, 10) . "...</p>";
                
                // Test customer creation
                echo "<h3>Testing Customer Creation</h3>";
                $test_customer_data = array(
                    'naziv' => 'Test Company ' . date('Y-m-d H:i:s'),
                    'adresa' => 'Test Address 123',
                    'mesto' => 'Test City',
                    'kontakt' => 'Test Contact',
                    'telefon' => '+381*********',
                    'email' => '<EMAIL>',
                    'pib' => '*********',
                    'mb' => '*********',
                    'komercijalista' => '1',
                    'klasa' => '1'
                );
                
                echo "<p>Attempting to create customer with data:</p>";
                echo "<pre>" . print_r($test_customer_data, true) . "</pre>";
                
                $create_url = $settings['api_url'] . $settings['api_alias'] . '/local.kupac.nov';
                echo "<p>Create URL: {$create_url}</p>";
                
                $create_response = wp_remote_post($create_url, array(
                    'headers' => array(
                        'Cookie' => 'PHPSESSID=' . $session_cookie
                    ),
                    'body' => $test_customer_data,
                    'timeout' => 60
                ));
                
                if (is_wp_error($create_response)) {
                    echo "<p style='color: red;'>Customer creation failed: " . $create_response->get_error_message() . "</p>";
                } else {
                    $create_code = wp_remote_retrieve_response_code($create_response);
                    $create_body = wp_remote_retrieve_body($create_response);
                    echo "<p>Create response code: {$create_code}</p>";
                    echo "<p>Create response body:</p>";
                    echo "<pre>" . htmlspecialchars($create_body) . "</pre>";
                    
                    if ($create_code === 200) {
                        echo "<p style='color: green;'>Customer creation appears successful!</p>";
                    } else {
                        echo "<p style='color: orange;'>Customer creation returned non-200 code</p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>No session cookie received</p>";
            }
        } else {
            echo "<p style='color: red;'>Authentication failed with code: {$response_code}</p>";
        }
    }
} else {
    echo "<p style='color: red;'>API settings not configured</p>";
}

// 4. Check database for any Wings-related data
echo "<h2>4. Database Check</h2>";
global $wpdb;

// Check user meta for Wings data
$wings_meta = $wpdb->get_results("
    SELECT um.user_id, um.meta_key, um.meta_value, u.user_email, u.display_name
    FROM {$wpdb->usermeta} um
    JOIN {$wpdb->users} u ON um.user_id = u.ID
    WHERE um.meta_key LIKE 'wings_%'
    LIMIT 20
");

echo "<p>Found " . count($wings_meta) . " Wings-related user meta entries:</p>";
foreach ($wings_meta as $meta) {
    echo "<li>User: {$meta->display_name} ({$meta->user_email}) - {$meta->meta_key}: {$meta->meta_value}</li>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>If you see issues, check:</p>";
echo "<ul>";
echo "<li>1. Are users being imported with wings_customer_id meta?</li>";
echo "<li>2. Are API credentials correct?</li>";
echo "<li>3. Is the customer creation API call format correct?</li>";
echo "</ul>";
?>
