# Check/Create Customer Feature

## 🎯 Overview

The Wings Customer Import plugin now includes a new feature that allows you to check if a customer exists in Wings by company name (naziv) and optionally create them if they don't exist.

## 📍 Access

Navigate to: **WordPress Admin → Wings Import → Manage Customers**

The new "Check/Create Customer in Wings" section appears at the top of the page.

## ✨ Features

### 🔍 **Check Customer Existence**
- Search for customers in Wings by exact company name (naziv)
- Case-insensitive matching
- Returns detailed customer information if found

### ➕ **Create Customer Option**
- Optional checkbox to create customer if not found
- Requires minimal information (naziv is mandatory)
- Creates customer with provided details in Wings

### 📋 **Form Fields**
- **Company Name (Naziv)** - Required field for searching/creating
- **Email** - Optional contact email
- **Contact Person** - Optional contact name
- **Phone** - Optional phone number
- **Address** - Optional street address
- **City** - Optional city/location
- **PIB** - Optional tax identification number
- **MB** - Optional business registration number

## 🎨 **How to Use**

### **Check Only Mode:**
1. Enter the company name (naziv) in the required field
2. Optionally fill in other fields for context
3. Leave "Create customer in Wings if not found" unchecked
4. Click "Check Customer"

**Results:**
- ✅ **Customer Found**: Shows existing customer details
- ⚠️ **Customer Not Found**: Shows message that customer doesn't exist

### **Check and Create Mode:**
1. Enter the company name (naziv) in the required field
2. Fill in additional fields (recommended for new customers)
3. Check "Create customer in Wings if not found"
4. Click "Check Customer"

**Results:**
- ✅ **Customer Found**: Shows existing customer details
- ➕ **Customer Created**: Creates new customer and shows success message
- ❌ **Creation Failed**: Shows error message if creation fails

## 🔧 **Technical Details**

### **API Integration**
- Uses Wings API endpoint `local.kupac.svi` for searching
- Uses Wings API endpoint `local.kupac.nov` for creating
- Implements exact name matching (case-insensitive)
- Searches up to 100 results for matching

### **New API Methods**
- `search_customers_by_naziv($naziv, $start, $length)` - Search customers by name
- `find_customer_by_naziv($naziv)` - Find exact match by name

### **AJAX Handler**
- `ajax_check_create_customer()` - Handles the check/create functionality
- Validates input data and permissions
- Returns structured JSON response

## 🎯 **Use Cases**

1. **Verify Customer Existence**: Check if a customer is already in Wings before manual entry
2. **Quick Customer Creation**: Create new customers directly from WordPress admin
3. **Data Validation**: Ensure no duplicate customers are created
4. **Customer Lookup**: Find existing customer details by company name

## 🔒 **Security**

- Requires `manage_options` capability
- Uses WordPress nonce verification
- Sanitizes all input data
- Validates required fields

## 🎨 **User Interface**

- Clean, responsive form layout
- Real-time result display
- Color-coded success/warning/error messages
- Auto-focus on company name field
- Clear form functionality
- Confirmation dialogs for successful creation

## 📝 **Response Format**

### **Customer Found:**
```json
{
  "exists": true,
  "customer": {
    "id": "123",
    "attributes": {
      "naziv": "Company Name",
      "email": "<EMAIL>",
      "kontakt": "John Doe",
      "telefon": "+*********",
      "adresa": "123 Main St",
      "mesto": "City Name"
    }
  },
  "message": "Customer \"Company Name\" already exists in Wings."
}
```

### **Customer Created:**
```json
{
  "exists": false,
  "created": true,
  "customer": { ... },
  "message": "Customer \"Company Name\" created successfully in Wings."
}
```

### **Customer Not Found:**
```json
{
  "exists": false,
  "created": false,
  "message": "Customer \"Company Name\" does not exist in Wings."
}
```

## 🚀 **Future Enhancements**

- Bulk customer checking/creation
- Import from CSV with check/create logic
- Advanced search filters
- Customer matching by other fields (email, PIB, etc.)
- Integration with WordPress user creation
