<?php
/**
 * Wings Import Admin Class
 * Handles admin interface and pages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Wings_Import_Admin {
    
    /**
     * Display main dashboard
     */
    public function display_dashboard() {
        $settings = get_option('wings_import_settings', array());
        ?>
        <div class="wrap">
            <h1><?php _e('Wings Customer Import Dashboard', 'wings-customer-import'); ?></h1>
            
            <?php if (get_transient('wings_import_activated')): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Wings Customer Import plugin activated successfully!', 'wings-customer-import'); ?></p>
                </div>
                <?php delete_transient('wings_import_activated'); ?>
            <?php endif; ?>
            
            <div id="wings-import-dashboard">
                <!-- Configuration Section -->
                <div class="wings-config-section">
                    <h2><?php _e('Configuration', 'wings-customer-import'); ?></h2>
                    <div class="wings-config-grid">
                        <div class="wings-config-item">
                            <label><?php _e('API URL:', 'wings-customer-import'); ?></label>
                            <input type="text" id="api_url" value="<?php echo esc_attr($settings['api_url'] ?? ''); ?>" readonly>
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Alias:', 'wings-customer-import'); ?></label>
                            <input type="text" id="api_alias" value="<?php echo esc_attr($settings['api_alias'] ?? ''); ?>">
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Username:', 'wings-customer-import'); ?></label>
                            <input type="text" id="api_username" value="<?php echo esc_attr($settings['api_username'] ?? ''); ?>">
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Password:', 'wings-customer-import'); ?></label>
                            <input type="password" id="api_password" value="<?php echo esc_attr($settings['api_password'] ?? ''); ?>">
                        </div>
                        <div class="wings-config-item">
                            <label><?php _e('Batch Size:', 'wings-customer-import'); ?></label>
                            <input type="number" id="batch_size" value="<?php echo esc_attr($settings['batch_size'] ?? 25); ?>" min="10" max="100">
                        </div>
                    </div>
                    
                    <div class="wings-actions">
                        <button id="test-connection" class="button button-secondary">
                            <?php _e('Test Connection', 'wings-customer-import'); ?>
                        </button>
                        <button id="start-import" class="button button-primary">
                            <?php _e('Start Import', 'wings-customer-import'); ?>
                        </button>
                        <button id="stop-import" class="button button-secondary" style="display: none;">
                            <?php _e('Stop Import', 'wings-customer-import'); ?>
                        </button>
                    </div>
                </div>
                
                <!-- Progress Section -->
                <div id="progress-section" class="wings-progress-section">
                    <h2><?php _e('Import Progress', 'wings-customer-import'); ?></h2>
                    
                    <div class="wings-status">
                        <span class="status-indicator" id="status-indicator"></span>
                        <span id="status-text"><?php _e('Ready to start', 'wings-customer-import'); ?></span>
                    </div>
                    
                    <div class="wings-progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div class="wings-progress-text">
                        <span id="progress-text">0 / 0 <?php _e('customers processed', 'wings-customer-import'); ?> (0%)</span>
                    </div>
                    
                    <div class="wings-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="stat-total">0</div>
                            <div class="stat-label"><?php _e('Total Found', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-processed">0</div>
                            <div class="stat-label"><?php _e('Processed', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-created">0</div>
                            <div class="stat-label"><?php _e('Created', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-updated">0</div>
                            <div class="stat-label"><?php _e('Updated', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-errors">0</div>
                            <div class="stat-label"><?php _e('Errors', 'wings-customer-import'); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stat-time">0s</div>
                            <div class="stat-label"><?php _e('Elapsed Time', 'wings-customer-import'); ?></div>
                        </div>
                    </div>
                    
                    <div id="error-details" class="wings-error-details" style="display: none;">
                        <h3><?php _e('Error Details', 'wings-customer-import'); ?></h3>
                        <ul id="error-list"></ul>
                    </div>
                    
                    <div class="wings-log-section">
                        <h3><?php _e('Live Log', 'wings-customer-import'); ?></h3>
                        <div class="wings-log-container" id="log-container">
                            <div class="log-entry"><?php _e('Ready to start import...', 'wings-customer-import'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Display settings page
     */
    public function display_settings() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        $settings = get_option('wings_import_settings', array());
        ?>
        <div class="wrap">
            <h1><?php _e('Wings Import Settings', 'wings-customer-import'); ?></h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('wings_import_settings', 'wings_import_settings_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('API URL', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="url" name="api_url" value="<?php echo esc_attr($settings['api_url'] ?? 'https://portal.wings.rs/api/v1/'); ?>" class="regular-text" />
                            <p class="description"><?php _e('Wings Portal API base URL', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Alias', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="text" name="api_alias" value="<?php echo esc_attr($settings['api_alias'] ?? ''); ?>" class="regular-text" />
                            <p class="description"><?php _e('Your Wings Portal alias', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Username', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="text" name="api_username" value="<?php echo esc_attr($settings['api_username'] ?? ''); ?>" class="regular-text" />
                            <p class="description"><?php _e('Wings Portal API username', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('API Password', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="password" name="api_password" value="<?php echo esc_attr($settings['api_password'] ?? ''); ?>" class="regular-text" />
                            <p class="description"><?php _e('Wings Portal API password', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Batch Size', 'wings-customer-import'); ?></th>
                        <td>
                            <input type="number" name="batch_size" value="<?php echo esc_attr($settings['batch_size'] ?? 25); ?>" min="10" max="100" />
                            <p class="description"><?php _e('Number of customers to process per batch (recommended: 25)', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Default User Role', 'wings-customer-import'); ?></th>
                        <td>
                            <select name="default_role">
                                <?php
                                $roles = wp_roles()->get_names();
                                $selected_role = $settings['default_role'] ?? 'Professionals';
                                foreach ($roles as $role_key => $role_name) {
                                    echo '<option value="' . esc_attr($role_key) . '"' . selected($selected_role, $role_key, false) . '>' . esc_html($role_name) . '</option>';
                                }
                                ?>
                            </select>
                            <p class="description"><?php _e('Default role for imported users', 'wings-customer-import'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
            
            <div class="wings-test-section">
                <h2><?php _e('Connection Test', 'wings-customer-import'); ?></h2>
                <button id="test-api-connection" class="button button-secondary">
                    <?php _e('Test API Connection', 'wings-customer-import'); ?>
                </button>
                <div id="test-results" style="margin-top: 10px;"></div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Display logs page
     */
    public function display_logs() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wings_import_logs';
        $per_page = 50;
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($page - 1) * $per_page;
        
        $total_logs = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        $logs = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT %d OFFSET %d",
            $per_page,
            $offset
        ));
        
        ?>
        <div class="wrap">
            <h1><?php _e('Import Logs', 'wings-customer-import'); ?></h1>
            
            <div class="tablenav top">
                <div class="alignleft actions">
                    <button id="clear-logs" class="button button-secondary">
                        <?php _e('Clear All Logs', 'wings-customer-import'); ?>
                    </button>
                </div>
                <?php
                $total_pages = ceil($total_logs / $per_page);
                if ($total_pages > 1) {
                    echo paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo;'),
                        'next_text' => __('&raquo;'),
                        'total' => $total_pages,
                        'current' => $page
                    ));
                }
                ?>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Date/Time', 'wings-customer-import'); ?></th>
                        <th><?php _e('Session ID', 'wings-customer-import'); ?></th>
                        <th><?php _e('Level', 'wings-customer-import'); ?></th>
                        <th><?php _e('Message', 'wings-customer-import'); ?></th>
                        <th><?php _e('Context', 'wings-customer-import'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($logs)): ?>
                        <tr>
                            <td colspan="5"><?php _e('No logs found.', 'wings-customer-import'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo esc_html($log->created_at); ?></td>
                                <td><?php echo esc_html(substr($log->session_id, -8)); ?></td>
                                <td>
                                    <span class="log-level log-level-<?php echo esc_attr($log->level); ?>">
                                        <?php echo esc_html(ucfirst($log->level)); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html($log->message); ?></td>
                                <td>
                                    <?php if (!empty($log->context)): ?>
                                        <details>
                                            <summary><?php _e('View Context', 'wings-customer-import'); ?></summary>
                                            <pre><?php echo esc_html($log->context); ?></pre>
                                        </details>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        if (!wp_verify_nonce($_POST['wings_import_settings_nonce'], 'wings_import_settings')) {
            wp_die(__('Security check failed', 'wings-customer-import'));
        }
        
        $settings = array(
            'api_url' => sanitize_url($_POST['api_url']),
            'api_alias' => sanitize_text_field($_POST['api_alias']),
            'api_username' => sanitize_text_field($_POST['api_username']),
            'api_password' => sanitize_text_field($_POST['api_password']),
            'batch_size' => intval($_POST['batch_size']),
            'default_role' => sanitize_text_field($_POST['default_role'])
        );
        
        update_option('wings_import_settings', $settings);
        
        echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'wings-customer-import') . '</p></div>';
    }

    /**
     * Display manage customers page
     */
    public function display_manage_customers() {
        ?>
        <div class="wrap">
            <h1><?php _e('Manage Wings Customers', 'wings-customer-import'); ?></h1>

            <!-- Check/Create Customer Section -->
            <div class="wings-check-create-section" style="background: #fff; padding: 20px; margin-bottom: 20px; border: 1px solid #ccd0d4; border-radius: 4px;">
                <h2><?php _e('Check/Create Customer in Wings', 'wings-customer-import'); ?></h2>
                <p><?php _e('Check if a customer exists in Wings by company name (naziv) and optionally create them if they don\'t exist.', 'wings-customer-import'); ?></p>

                <form id="wings-check-create-form" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div class="form-group">
                        <label for="check-naziv" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('Company Name (Naziv)', 'wings-customer-import'); ?> <span style="color: red;">*</span>
                        </label>
                        <input type="text" id="check-naziv" name="naziv" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-email" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('Email', 'wings-customer-import'); ?>
                        </label>
                        <input type="email" id="check-email" name="email" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-kontakt" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('Contact Person', 'wings-customer-import'); ?>
                        </label>
                        <input type="text" id="check-kontakt" name="kontakt" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-telefon" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('Phone', 'wings-customer-import'); ?>
                        </label>
                        <input type="text" id="check-telefon" name="telefon" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-adresa" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('Address', 'wings-customer-import'); ?>
                        </label>
                        <input type="text" id="check-adresa" name="adresa" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-mesto" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('City', 'wings-customer-import'); ?>
                        </label>
                        <input type="text" id="check-mesto" name="mesto" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-pib" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('PIB', 'wings-customer-import'); ?>
                        </label>
                        <input type="text" id="check-pib" name="pib" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>

                    <div class="form-group">
                        <label for="check-mb" style="display: block; margin-bottom: 5px; font-weight: 600;">
                            <?php _e('MB', 'wings-customer-import'); ?>
                        </label>
                        <input type="text" id="check-mb" name="mb" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    </div>
                </form>

                <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 15px;">
                    <label style="display: flex; align-items: center; gap: 5px;">
                        <input type="checkbox" id="create-if-not-exists" />
                        <?php _e('Create customer in Wings if not found', 'wings-customer-import'); ?>
                    </label>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button type="button" id="check-customer-btn" class="button button-primary">
                        <?php _e('Check Customer', 'wings-customer-import'); ?>
                    </button>
                    <button type="button" id="clear-form-btn" class="button button-secondary">
                        <?php _e('Clear Form', 'wings-customer-import'); ?>
                    </button>
                </div>

                <div id="check-result" style="margin-top: 15px; padding: 10px; border-radius: 4px; display: none;"></div>
            </div>

            <div id="wings-customers-manager">
                <!-- Search and Filters -->
                <div class="wings-customers-toolbar">
                    <div class="wings-search-box">
                        <input type="text" id="customer-search" placeholder="<?php _e('Search customers...', 'wings-customer-import'); ?>" />
                        <button id="search-customers" class="button"><?php _e('Search', 'wings-customer-import'); ?></button>
                        <button id="clear-search" class="button"><?php _e('Clear', 'wings-customer-import'); ?></button>
                    </div>

                    <div class="wings-actions-box">
                        <button id="refresh-customers" class="button button-secondary">
                            <?php _e('Refresh', 'wings-customer-import'); ?>
                        </button>
                        <button id="export-customers" class="button button-secondary">
                            <?php _e('Export CSV', 'wings-customer-import'); ?>
                        </button>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="wings-customers-table-container">
                    <table id="wings-customers-table" class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th class="check-column">
                                    <input type="checkbox" id="select-all-customers" />
                                </th>
                                <th><?php _e('Company Name', 'wings-customer-import'); ?></th>
                                <th><?php _e('Contact Person', 'wings-customer-import'); ?></th>
                                <th><?php _e('Email', 'wings-customer-import'); ?></th>
                                <th><?php _e('Phone', 'wings-customer-import'); ?></th>
                                <th><?php _e('City', 'wings-customer-import'); ?></th>
                                <th><?php _e('Wings ID', 'wings-customer-import'); ?></th>
                                <th><?php _e('Status', 'wings-customer-import'); ?></th>
                                <th><?php _e('Actions', 'wings-customer-import'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                            <tr>
                                <td colspan="9" class="loading-row">
                                    <div class="wings-loading-spinner"></div>
                                    <?php _e('Loading customers...', 'wings-customer-import'); ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="wings-pagination">
                    <div class="pagination-info">
                        <span id="pagination-info"><?php _e('Loading...', 'wings-customer-import'); ?></span>
                    </div>
                    <div class="pagination-controls">
                        <button id="prev-page" class="button" disabled><?php _e('Previous', 'wings-customer-import'); ?></button>
                        <span id="page-numbers"></span>
                        <button id="next-page" class="button" disabled><?php _e('Next', 'wings-customer-import'); ?></button>
                    </div>
                </div>
            </div>

            <!-- Edit Customer Modal -->
            <div id="edit-customer-modal" class="wings-modal" style="display: none;">
                <div class="wings-modal-content">
                    <div class="wings-modal-header">
                        <h2><?php _e('Edit Customer', 'wings-customer-import'); ?></h2>
                        <span class="wings-modal-close">&times;</span>
                    </div>

                    <div class="wings-modal-body">
                        <form id="edit-customer-form">
                            <input type="hidden" id="edit-user-id" />

                            <div class="wings-form-grid">
                                <div class="wings-form-group">
                                    <label><?php _e('Company Name', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-company-name" name="display_name" required />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Email', 'wings-customer-import'); ?></label>
                                    <input type="email" id="edit-email" name="user_email" required />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('First Name', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-first-name" name="first_name" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Last Name', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-last-name" name="last_name" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Phone', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-phone" name="billing_phone" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Address', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-address" name="billing_address_1" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('City', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-city" name="billing_city" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Postcode', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-postcode" name="billing_postcode" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Wings Customer ID', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-wings-id" name="wings_customer_id" readonly />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('PIB', 'wings-customer-import'); ?></label>
                                    <input type="text" id="edit-pib" name="wings_pib" />
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('Status', 'wings-customer-import'); ?></label>
                                    <select id="edit-status" name="wings_status">
                                        <option value="K"><?php _e('Active (K)', 'wings-customer-import'); ?></option>
                                        <option value="N"><?php _e('Inactive (N)', 'wings-customer-import'); ?></option>
                                    </select>
                                </div>

                                <div class="wings-form-group">
                                    <label><?php _e('User Role', 'wings-customer-import'); ?></label>
                                    <select id="edit-role" name="role">
                                        <?php
                                        $roles = wp_roles()->get_names();
                                        foreach ($roles as $role_key => $role_name) {
                                            echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role_name) . '</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <div class="wings-modal-actions">
                                <button type="submit" class="button button-primary">
                                    <?php _e('Save Changes', 'wings-customer-import'); ?>
                                </button>
                                <button type="button" class="button wings-modal-cancel">
                                    <?php _e('Cancel', 'wings-customer-import'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Delete Confirmation Modal -->
            <div id="delete-customer-modal" class="wings-modal" style="display: none;">
                <div class="wings-modal-content wings-modal-small">
                    <div class="wings-modal-header">
                        <h2><?php _e('Delete Customer', 'wings-customer-import'); ?></h2>
                        <span class="wings-modal-close">&times;</span>
                    </div>

                    <div class="wings-modal-body">
                        <p><?php _e('Are you sure you want to delete this customer?', 'wings-customer-import'); ?></p>
                        <p><strong id="delete-customer-name"></strong></p>
                        <p class="wings-warning"><?php _e('This action cannot be undone.', 'wings-customer-import'); ?></p>

                        <div class="wings-modal-actions">
                            <button id="confirm-delete" class="button button-primary button-danger">
                                <?php _e('Delete Customer', 'wings-customer-import'); ?>
                            </button>
                            <button class="button wings-modal-cancel">
                                <?php _e('Cancel', 'wings-customer-import'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get customers data for AJAX
     */
    public function get_customers_data($page = 1, $per_page = 20, $search = '') {
        $args = array(
            'meta_query' => array(
                array(
                    'key' => 'wings_customer_id',
                    'compare' => 'EXISTS'
                )
            ),
            'number' => $per_page,
            'offset' => ($page - 1) * $per_page,
            'orderby' => 'display_name',
            'order' => 'ASC'
        );

        if (!empty($search)) {
            $args['search'] = '*' . $search . '*';
            $args['search_columns'] = array('display_name', 'user_email', 'user_login');
        }

        $users_query = new WP_User_Query($args);
        $users = $users_query->get_results();

        // Get total count for pagination
        $total_args = $args;
        unset($total_args['number'], $total_args['offset']);
        $total_query = new WP_User_Query($total_args);
        $total_users = $total_query->get_total();

        $customers_data = array();

        foreach ($users as $user) {
            $customer = array(
                'id' => $user->ID,
                'display_name' => $user->display_name,
                'user_email' => $user->user_email,
                'first_name' => get_user_meta($user->ID, 'first_name', true),
                'last_name' => get_user_meta($user->ID, 'last_name', true),
                'billing_phone' => get_user_meta($user->ID, 'billing_phone', true),
                'billing_city' => get_user_meta($user->ID, 'billing_city', true),
                'billing_address_1' => get_user_meta($user->ID, 'billing_address_1', true),
                'billing_postcode' => get_user_meta($user->ID, 'billing_postcode', true),
                'wings_customer_id' => get_user_meta($user->ID, 'wings_customer_id', true),
                'wings_pib' => get_user_meta($user->ID, 'wings_pib', true),
                'wings_status' => get_user_meta($user->ID, 'wings_status', true),
                'wings_last_sync' => get_user_meta($user->ID, 'wings_last_sync', true),
                'role' => !empty($user->roles) ? $user->roles[0] : 'Professionals'
            );

            $customers_data[] = $customer;
        }

        return array(
            'customers' => $customers_data,
            'total' => $total_users,
            'page' => $page,
            'per_page' => $per_page,
            'total_pages' => ceil($total_users / $per_page)
        );
    }

    /**
     * Update customer data
     */
    public function update_customer($user_id, $customer_data) {
        if (!$user_id || !is_array($customer_data)) {
            return new WP_Error('invalid_data', __('Invalid customer data', 'wings-customer-import'));
        }

        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return new WP_Error('user_not_found', __('Customer not found', 'wings-customer-import'));
        }

        // Update WordPress user fields
        $user_update_data = array('ID' => $user_id);

        if (isset($customer_data['display_name'])) {
            $user_update_data['display_name'] = sanitize_text_field($customer_data['display_name']);
        }

        if (isset($customer_data['user_email'])) {
            $user_update_data['user_email'] = sanitize_email($customer_data['user_email']);
        }

        if (isset($customer_data['role'])) {
            $user_update_data['role'] = sanitize_text_field($customer_data['role']);
        }

        $result = wp_update_user($user_update_data);

        if (is_wp_error($result)) {
            return $result;
        }

        // Update user meta fields
        $meta_fields = array(
            'first_name', 'last_name', 'billing_phone', 'billing_address_1',
            'billing_city', 'billing_postcode', 'wings_pib', 'wings_status'
        );

        foreach ($meta_fields as $field) {
            if (isset($customer_data[$field])) {
                update_user_meta($user_id, $field, sanitize_text_field($customer_data[$field]));
            }
        }

        // Update last sync time
        update_user_meta($user_id, 'wings_last_sync', current_time('mysql'));

        return array(
            'success' => true,
            'message' => __('Customer updated successfully', 'wings-customer-import'),
            'customer_id' => $user_id
        );
    }

    /**
     * Delete customer
     */
    public function delete_customer($user_id) {
        if (!$user_id) {
            return new WP_Error('invalid_data', __('Invalid customer ID', 'wings-customer-import'));
        }

        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return new WP_Error('user_not_found', __('Customer not found', 'wings-customer-import'));
        }

        // Check if user has Wings customer ID (safety check)
        $wings_id = get_user_meta($user_id, 'wings_customer_id', true);
        if (empty($wings_id)) {
            return new WP_Error('not_wings_customer', __('This user is not a Wings customer', 'wings-customer-import'));
        }

        $result = wp_delete_user($user_id);

        if (!$result) {
            return new WP_Error('delete_failed', __('Failed to delete customer', 'wings-customer-import'));
        }

        return array(
            'success' => true,
            'message' => __('Customer deleted successfully', 'wings-customer-import'),
            'customer_id' => $user_id
        );
    }
}
